<?php

namespace App\Services\StockBatches;

use Illuminate\Support\Facades\Cache;
use App\Http\Resources\StockBatchResource;
use App\Models\Product;
use App\Models\Stock;
use App\Models\StockBatch;
use App\Repositories\StockBatchRepository;
use App\Repositories\StockBatchSkuRepository;
use App\Repositories\StockSkuRepository;
use App\Services\BaseAPIService;
use App\Services\ProductSkus\ProductSkuService;
use App\Services\StockSkus\StockSkuService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class StockBatchService extends BaseAPIService
{
    protected $storeFields = [
        'stock_id',
        'name',
        'price',
        'quantity',
        'type',
        'transfer_to_stock_id',
        'note',
        'photos',
        'approve_staff_id',
        'approve_time',
        'handle_date',
        'status',
    ];
    protected $updateFields = [
        'stock_id',
        'name',
        'price',
        'quantity',
        'type',
        'transfer_to_stock_id',
        'note',
        'photos',
        'approve_staff_id',
        'approve_time',
        'handle_date',
        'status',
    ];

    private $stockBatchSkuRepo;
    private $stockSkuRepo;
    private $stockSkuService;
    private $productSkuService;

    public function __construct()
    {
        parent::__construct();
        $this->repo = app(StockBatchRepository::class);
        $this->stockBatchSkuRepo = app(StockBatchSkuRepository::class);
        $this->stockSkuRepo = app(StockSkuRepository::class);
        $this->stockSkuService = app(StockSkuService::class);
        $this->productSkuService = app(ProductSkuService::class);
    }

    public function paginate($search, $page, $perPage, $columns = ['*'], $sortBy = 'id', $orderBy = 'desc'): array
    {
        $query = $this->repo->allQuery($search);
        $query->with(['stock', 'transferStock', 'approver', 'creator']);
        if (!empty($sortBy) && !empty($orderBy)) {
            $query->orderBy($sortBy, $orderBy);
        }
        $data = $query->paginate($perPage, $columns, 'page', $page);
        return [
            'total' => $data->total(),
            'data' => StockBatchResource::collection($data->items()),
        ];
    }

    public function store($input, $user)
    {
        try {
            $dataStockBatch = $input['stock_batch'];
            $dataSkus = $input['stock_batch_skus'];
            DB::beginTransaction();
            $stockBatch = parent::store($dataStockBatch, $user);
            if ($stockBatch) {
                $userId = $user->id;
                $dataSkus = array_map(function ($item) use ($userId) {
                    $item['reason'] = $item['reason'] ?? "";
                    $item['creator_id'] = $userId;
                    $item['updater_id'] = $userId;
                    return $item;
                }, $dataSkus);
                $stockBatch->stockBatchSkus()->createMany($dataSkus);
            }
            DB::commit();
            return $stockBatch;
        } catch (\Exception $e) {
            Log::channel('factory')->info(__CLASS__ . '@' . __FUNCTION__, $input);
            Log::channel('factory')->error($e->getMessage(), $e->getTrace());
            DB::rollBack();
            throw $e;
        }
    }

    public function getStockBatchWithAllSkus(StockBatch $stockBatch, Request $request)
    {
        $type = $request->get('type');
        $allSkusData = [];
        if ($type != StockBatch::TYPE_IMPORT) {
            $paginateStockSkus = $this->stockSkuService->paginateStockSkusForUpdateBatch($request, $stockBatch->id);

            $allSkusData = [
                'total' => $paginateStockSkus->total(),
                'data' => $paginateStockSkus->items()
            ];
        } else {
            $request->merge([
                'service' => Product::SERVICE_PRODUCTION,
            ]);
            $dataAllSkus = $this->productSkuService->paginateSkusForFactory($request, $stockBatch->id);

            $allSkusData = [
                'total' => $dataAllSkus->total(),
                'data' => $dataAllSkus->items()
            ];
        }
        return $allSkusData;
    }

    public function getStockBatchWithInputtedSkus(StockBatch $stockBatch, Request $request)
    {
        $type = $request->get('type');
        $cacheKey = "stock_batch_inputted_skus_{$stockBatch->id}_type-{$type}";

        // Try to get from cache first
        $inputtedSkusData = Cache::remember($cacheKey, 3600, function () use ($stockBatch) {
            $stockBatchSkus = $stockBatch->stockBatchSkus()
                ->join('product_skus', 'product_skus.id', '=', 'stock_batch_skus.product_sku_id')
                ->join('stock_skus', 'stock_skus.product_sku_id', '=', 'stock_batch_skus.product_sku_id') // for type export
                ->select([
                    'stock_batch_skus.id',
                    'stock_batch_skus.product_id',
                    'stock_batch_skus.product_sku_id',
                    DB::raw('COALESCE(`stock_batch_skus`.`product_sku_name`, `product_skus`.`name`) AS `product_sku_name`'),
                    'stock_batch_skus.reason',
                    'stock_batch_skus.quantity',
                    'stock_batch_skus.unit_price',
                    'stock_skus.stock_id', // for type export
                    'stock_skus.quantity AS stock_sku_quantity', // for type export
                ])
                ->get();

            return [
                'total' => $stockBatchSkus->count(),
                'data' => $stockBatchSkus->toArray()
            ];
        });

        return $inputtedSkusData;
    }

    public function showStockBatch(StockBatch $stockBatch, Request $request)
    {
        $page = $request->get('page', 1);
        $limit = $request->get('limit', 25);
        $sortBy = $request->get('sortBy', 'id');
        $orderBy = $request->get('orderBy', 'desc');

        $cacheKey = "stock_batch_show_{$stockBatch->id}_page-{$page}_limit-{$limit}_sort-{$sortBy}-{$orderBy}";

        // Cache vĩnh viễn - sẽ được clear khi update stock batch
        return Cache::rememberForever($cacheKey, function () use ($stockBatch, $page, $limit, $sortBy, $orderBy) {
            $stockBatchSkus = $stockBatch->stockBatchSkus()
                ->join('product_skus', 'product_skus.id', '=', 'stock_batch_skus.product_sku_id')
                ->join('stock_skus', 'stock_skus.product_sku_id', '=', 'stock_batch_skus.product_sku_id') // for type export
                ->select([
                    'stock_batch_skus.id',
                    'stock_batch_skus.product_id',
                    'stock_batch_skus.product_sku_id',
                    DB::raw('COALESCE(`stock_batch_skus`.`product_sku_name`, `product_skus`.`name`) AS `product_sku_name`'),
                    'stock_batch_skus.reason',
                    'stock_batch_skus.quantity',
                    'stock_batch_skus.unit_price',
                    'stock_skus.stock_id', // for type export
                    'stock_skus.quantity AS stock_sku_quantity', // for type export
                ])
                ->orderBy($sortBy, $orderBy)
                ->paginate($limit, ['*'], 'page', $page);

            return [
                'total' => $stockBatchSkus->total(),
                'data' => $stockBatchSkus->items()
            ];
        });
    }

    public function update($id, $input, $user)
    {
        try {
            $dataStockBatch = $input['stock_batch'];
            $dataSkus = $input['stock_batch_skus'];
            $dataSkuIds = collect($dataSkus)->pluck('id');

            DB::beginTransaction();
            $stockBatch = parent::update($id, $dataStockBatch, $user);
            $this->deleteSkus($stockBatch, $dataSkuIds, $user->id);
            $this->createOrUpdateStockBatchSkus($dataSkus, $id, $user->id);

            // Reset cache if data has changed
            $stockBatch = $this->repo->find($id);
            if ($this->shouldResetCache($stockBatch, $dataSkus)) {
                $this->clearInputtedSkusCache($stockBatch);
            }

            DB::commit();
            return $stockBatch;
        } catch (\Exception $e) {
            Log::channel('factory')->info(__CLASS__ . '@' . __FUNCTION__, $input);
            Log::channel('factory')->error($e->getMessage(), $e->getTrace());
            DB::rollBack();
            throw $e;
        }
    }

    public function approve($stockBatch, $input, $user)
    {
        try {
            $dataStockBatch = $input['stock_batch'];
            $dataSkus = $input['stock_batch_skus'];
            $dataSkus = array_map(function ($item) {
                unset($item['quantity']);
                return $item;
            }, $dataSkus);
            $dataSkuIds = collect($dataSkus)->pluck('id');
            $dataStockBatch['status'] = StockBatch::STATUS_APPROVED;
            $dataStockBatch['approve_staff_id'] = $user->id;
            $dataStockBatch['approve_time'] = Carbon::now();

            DB::beginTransaction();
            $stockBatch = parent::update($stockBatch->id, $dataStockBatch, $user);
            $this->deleteSkus($stockBatch, $dataSkuIds, $user->id);
            $stockBatchSkus = $this->createOrUpdateStockBatchSkus($dataSkus, $stockBatch->id, $user->id);
            $stockSkuTrans = $this->createManyStockSkuTrans($stockBatchSkus, $stockBatch, $user->id);
            $stockSkuTrans->each(function ($skuTransItem) use ($user, $stockBatch) {
                $this->updateOrCreateStockSku($skuTransItem, $user->id, $stockBatch);
            });

            // Reset cache if data has changed
            if ($this->shouldResetCache($stockBatch, $dataSkus)) {
                $this->clearInputtedSkusCache($stockBatch);
            }

            DB::commit();
            return $stockBatch;
        } catch (\Exception $e) {
            Log::channel('factory')->info(__CLASS__ . '@' . __FUNCTION__, $input);
            Log::channel('factory')->error($e->getMessage(), $e->getTrace());
            DB::rollBack();
            throw $e;
        }
    }

    public function cancel($stockBatch, $user)
    {
        try {
            DB::beginTransaction();
            $stockBatch->update([
                'status' => StockBatch::STATUS_CANCELED,
                'approve_staff_id' => $user->id,
                'approve_time' => Carbon::now(),
                'updater_id' => $user->id
            ]);
            DB::commit();
            return $stockBatch;
        } catch (\Exception $e) {
            Log::channel('factory')->info(__CLASS__ . '@' . __FUNCTION__);
            Log::channel('factory')->error($e->getMessage(), $e->getTrace());
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Check if cache should be reset by comparing current data with new data
     */
    private function shouldResetCache($stockBatch, $newDataSkus)
    {
        // Get current stock batch skus
        $currentSkus = $stockBatch->stockBatchSkus()->get();

        // Convert to comparable format
        $currentSkusData = $currentSkus->map(function ($sku) {
            return [
                'id' => data_get($sku, 'id'),
                'product_sku_id' => data_get($sku, 'product_sku_id'),
                'quantity' => data_get($sku, 'quantity_final', data_get($sku, 'quantity', 0)),
                'unit_price' => data_get($sku, 'unit_price'),
                'reason' => data_get($sku, 'reason'),
            ];
        })->sortBy('id')->values()->toArray();

        $newSkusData = collect($newDataSkus)->map(function ($sku) {
            return [
                'id' => $sku['id'] ?? null,
                'product_sku_id' => data_get($sku, 'product_sku_id'),
                'quantity' => data_get($sku, 'quantity_final', data_get($sku, 'quantity', 0)),
                'unit_price' => data_get($sku, 'unit_price'),
                'reason' => data_get($sku, 'reason'),
            ];
        })->sortBy('id')->values()->toArray();

        // Compare data - if different, should reset cache
        return json_encode($currentSkusData) != json_encode($newSkusData);
    }

    private function clearInputtedSkusCache($stockBatch)
    {
        // Clear inputted skus cache
        $types = [StockBatch::TYPE_IMPORT, StockBatch::TYPE_EXPORT, StockBatch::TYPE_TRANSFER];
        foreach ($types as $type) {
            Cache::forget("stock_batch_inputted_skus_{$stockBatch->id}_type-{$type}");
        }
    }

    private function createOrUpdateStockBatchSkus($dataSkus, $stockBatchId, $userId)
    {
        $stockBatchSkus = collect();
        foreach ($dataSkus as $dataSku) {
            $dataSku['updater_id'] = $userId;
            if (isset($dataSku['id'])) {
                $stockBatchSkus->push($this->stockBatchSkuRepo->update($dataSku, $dataSku['id']));
            } else {
                $dataSku['stock_batch_id'] = $stockBatchId;
                $dataSku['creater_id'] = $userId;
                $stockBatchSkus->push($this->stockBatchSkuRepo->create($dataSku));
            }
        }
        return $stockBatchSkus;
    }

    private function createManyStockSkuTrans($stockBatchSkus, $stockBatch, $userId)
    {
        $dataStockSkuTrans = $stockBatchSkus->map(function ($batchSku) use ($stockBatch, $userId) {
            $data = collect($batchSku)->only(['product_id', 'product_sku_id', 'reason'])->toArray();
            $data['quantity'] = $stockBatch->type == StockBatch::TYPE_IMPORT ?
                ($batchSku->quantity_final ?? $batchSku->quantity) :
                - ($batchSku->quantity_final ?? $batchSku->quantity);
            $data['stock_id'] = $stockBatch->stock_id;
            $data['handle_staff_id'] = $userId;
            $data['handle_date'] = $stockBatch->handle_date;
            $data['creator_id'] = $userId;
            $data['updater_id'] = $userId;
            return $data;
        })->toArray();
        return $stockBatch->stockSkuTrans()->createMany($dataStockSkuTrans);
    }

    /**
     * Updates or creates a stock SKU based on the given item data.
     *
     * @param \App\Models\StockSkuTransaction $item
     * @param int $userId
     * @param \App\Models\StockBatch $stockBatch
     *
     * @return void
     */
    private function updateOrCreateStockSku($item, $userId, $stockBatch)
    {
        $stockSku = $this->stockSkuService->getStockSku(
            collect($item)->only(['stock_id', 'product_sku_id'])->toArray()
        );
        if ($stockSku !== null) {
            $stockSku->update([
                'quantity' => $stockSku->quantity + $item->quantity,
                'updater_id' => $userId
            ]);
            if ($stockBatch->type == StockBatch::TYPE_TRANSFER) {
                $this->handleTransferStockSku($item, $userId, $stockBatch);
            }
        } else {
            $this->createStockSku(
                $item->stock_id,
                $item->product_id,
                $item->product_sku_id,
                $item->quantity,
                $userId
            );
        }
    }

    /**
     * Handles the transfer of stock SKUs for a given stock batch.
     *
     * @param \App\Models\StockSkuTransaction $item
     * @param int $userId
     * @param \App\Models\StockBatch $stockBatch
     *
     * @return void
     */
    private function handleTransferStockSku($item, $userId, $stockBatch)
    {
        $transferStockSku = $this->stockSkuService->getStockSku([
            'stock_id' => $stockBatch->transfer_to_stock_id,
            'product_sku_id' => $item->product_sku_id
        ]);
        $addedQuantity = -$item->quantity;
        if ($transferStockSku !== null) {
            $transferStockSku->update([
                'quantity' => $transferStockSku->quantity + $addedQuantity,
                'updater_id' => $userId
            ]);
        } else {
            $this->createStockSku(
                $stockBatch->transfer_to_stock_id,
                $item->product_id,
                $item->product_sku_id,
                $addedQuantity,
                $userId
            );
        }
    }

    private function createStockSku($stockId, $productId, $productSkuId, $quantity, $userId)
    {
        return $this->stockSkuRepo->create([
            'stock_id' => $stockId,
            'product_id' => $productId,
            'product_sku_id' => $productSkuId,
            'quantity' => $quantity,
            'creator_id' => $userId,
            'updater_id' => $userId,
        ]);
    }

    private function deleteSkus($stockBatch, $skuIds, $userId)
    {
        $deleteSkus = $stockBatch->stockBatchSkus->reject(function ($sku) use ($skuIds) {
            return $skuIds->contains($sku->id);
        });
        $deleteSkus->each(function ($sku) use ($userId) {
            $this->stockBatchSkuRepo->update(['deleter_id' => $userId], $sku->id);
            $sku->delete();
        });
    }
}

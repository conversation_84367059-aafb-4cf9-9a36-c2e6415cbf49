1754032948O:17:"App\Models\Seller":34:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:7:"sellers";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:17:{s:2:"id";i:9;s:4:"name";s:7:"NB_Team";s:8:"password";s:60:"$2y$10$gfZViW2OyOunw5cvlJbBDe/DMdDBbtPY5qdwu8LS1hqQvkmor1Fwq";s:5:"email";s:19:"<EMAIL>";s:3:"tel";N;s:7:"balance";i:-1245413;s:12:"autopay_time";i:3600;s:16:"telegram_setting";N;s:18:"default_factory_id";s:1:"1";s:24:"default_designer_team_id";N;s:14:"seller_tier_id";i:1;s:22:"allow_negative_balance";i:1;s:9:"parent_id";N;s:10:"created_at";s:19:"2025-05-30 03:41:25";s:10:"updated_at";s:19:"2025-07-31 00:05:53";s:10:"deleted_at";N;s:6:"status";i:1;}s:11:" * original";a:17:{s:2:"id";i:9;s:4:"name";s:7:"NB_Team";s:8:"password";s:60:"$2y$10$gfZViW2OyOunw5cvlJbBDe/DMdDBbtPY5qdwu8LS1hqQvkmor1Fwq";s:5:"email";s:19:"<EMAIL>";s:3:"tel";N;s:7:"balance";i:-1245413;s:12:"autopay_time";i:3600;s:16:"telegram_setting";N;s:18:"default_factory_id";s:1:"1";s:24:"default_designer_team_id";N;s:14:"seller_tier_id";i:1;s:22:"allow_negative_balance";i:1;s:9:"parent_id";N;s:10:"created_at";s:19:"2025-05-30 03:41:25";s:10:"updated_at";s:19:"2025-07-31 00:05:53";s:10:"deleted_at";N;s:6:"status";i:1;}s:10:" * changes";a:0:{}s:8:" * casts";a:7:{s:16:"telegram_setting";s:4:"json";s:6:"status";s:7:"integer";s:7:"balance";s:7:"integer";s:18:"default_factory_id";s:7:"integer";s:24:"default_designer_team_id";s:7:"integer";s:22:"allow_negative_balance";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:12:{i:0;s:4:"name";i:1;s:8:"password";i:2;s:5:"email";i:3;s:3:"tel";i:4;s:7:"balance";i:5;s:12:"autopay_time";i:6;s:16:"telegram_setting";i:7;s:18:"default_factory_id";i:8;s:24:"default_designer_team_id";i:9;s:6:"status";i:10;s:22:"allow_negative_balance";i:11;s:9:"parent_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:20:" * rememberTokenName";s:14:"remember_token";s:6:"filter";a:4:{i:0;s:4:"name";i:1;s:5:"email";i:2;s:5:"phone";i:3;s:9:"parent_id";}s:16:" * forceDeleting";b:0;s:14:" * accessToken";O:35:"Laravel\Sanctum\PersonalAccessToken":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:22:"personal_access_tokens";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:9:{s:2:"id";i:666;s:14:"tokenable_type";s:17:"App\Models\Seller";s:12:"tokenable_id";i:9;s:4:"name";s:12:"seller-token";s:5:"token";s:64:"a59e80380627de5f85b80542152a408999737544f3dffc24c5c2aa2916d1b2a2";s:9:"abilities";s:5:"["*"]";s:12:"last_used_at";N;s:10:"created_at";s:19:"2025-07-31 00:06:02";s:10:"updated_at";s:19:"2025-07-31 00:06:02";}s:11:" * original";a:9:{s:2:"id";i:666;s:14:"tokenable_type";s:17:"App\Models\Seller";s:12:"tokenable_id";i:9;s:4:"name";s:12:"seller-token";s:5:"token";s:64:"a59e80380627de5f85b80542152a408999737544f3dffc24c5c2aa2916d1b2a2";s:9:"abilities";s:5:"["*"]";s:12:"last_used_at";N;s:10:"created_at";s:19:"2025-07-31 00:06:02";s:10:"updated_at";s:19:"2025-07-31 00:06:02";}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:9:"abilities";s:4:"json";s:12:"last_used_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:9:"tokenable";r:1;}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:1:{i:0;s:5:"token";}s:10:" * visible";a:0:{}s:11:" * fillable";a:3:{i:0;s:4:"name";i:1;s:5:"token";i:2;s:9:"abilities";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}
APP_NAME=Laravel
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_URL=http://localhost

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=dp
DB_USERNAME=root
DB_PASSWORD=12345678

BROADCAST_DRIVER=pusher
CACHE_DRIVER=file
FILESYSTEM_DRIVER=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_HOST=mailhog
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS=null
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

GOOGLE_CLOUD_PROJECT_ID=total-biplane-438
GOOGLE_CLOUD_KEY_FILE_PATH=/root/ditechprintapi/storage/gcs_key.json
GOOGLE_CLOUD_STORAGE_BUCKET=cdn-asia.ditechmedia.net
GOOGLE_CLOUD_STORAGE_PATH_PREFIX="bi"
GOOGLE_CLOUD_STORAGE_API_URI=https://storage.googleapis.com/cdn-asia.ditechmedia.net

DROPBOX_AUTH_TOKEN=
DROPBOX_APP_KEY=
DROPBOX_APP_SECRET=
DROPBOX_REFRESH_TOKEN=
DROPBOX_FOLDER=uploads
DROPBOX_URL=

PUSHER_APP_ID=local
PUSHER_APP_KEY=local
PUSHER_APP_SECRET=local
PUSHER_APP_CLUSTER=mt1
PUSHER_APP_SCHEME=https
PUSHER_APP_PORT=6001
PUSHER_APP_HOST=***************
LARAVEL_WEBSOCKETS_PORT=6001
LARAVEL_WEBSOCKETS_DOMAIN=***************

MIX_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
MIX_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

ALLOWED_INTERNAL_URLS=http://vinaway.test:8080

VIETTEL_POST_AUTH_URL=

17TRACK_API_KEY=

TELEGRAM_BOT_TOPUP_TOKEN=
TELEGRAM_TOP_CHAT_IDS=

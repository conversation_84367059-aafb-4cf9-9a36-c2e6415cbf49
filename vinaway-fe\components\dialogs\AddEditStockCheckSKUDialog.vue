<script setup>
import { STOCK_CHECK_STATUS } from '@/helpers/ConstantHelper'
import { formatNumber } from '@/helpers/Helper'

const props = defineProps({
  modelValue: {
    type: Object,
    required: false,
    default: Object,
  },
  isApprove: {
    type: Boolean,
    required: false,
    default: false,
  }
})

const emit = defineEmits([
  'change',
  'update:modelValue',
  'update:isApprove'
])

const { filter, updateOptions, callback } = useFilter({
  page: 1,
  limit: 25,
})

const headers = computed(() => [
{
    title: 'Sku name',
    key: 'name',
    width: '15%'
  },
  {
    title: 'Quantity expect',
    key: 'quantity_expect',
    width: '10%'
  },
  {
    title: 'Quantity real',
    key: 'quantity_real',
    width: '15%',
    sortable: false
  },
  {
    title: 'Quantity diff',
    key: 'quantity_diff',
    width: '10%',
    sortable: false
  },
  {
    title: 'Note',
    key: 'note',
    sortable: false
  },
])

const isDialogVisible = ref(false)
const refForm = ref()
const isResetForm = ref(false)
const tableLoading = ref(false)
const toast = reactive({ loading: false, message: null, color: 'error' })
const paginationKey = ref(0)

const stockCheckSkus = ref([])
const totalStockCheckSkus = ref(0)
const inputedSkuItems = ref([])

const isCheckingStatus = computed(() => props.modelValue?.status === STOCK_CHECK_STATUS.INIT || props.modelValue?.status === STOCK_CHECK_STATUS.IN_PROGRESS)
const isHasOneSku = computed(() => stockCheckSkus.value.some(item => item.quantity_real))
const quantityDiff = computed(() => item => item.quantity_real ? item.quantity_real - item.quantity_expect : 0)

const confirmDialog = reactive({
  show: false,
  type: "ok",
})

const getDataStockCheckSkus = async (item) => {
  const params = filter
  tableLoading.value = true
  const { data } = await useApi(`/factory/stock-checks/${item.id}/skus`, { params })
  stockCheckSkus.value = data.value?.data.map(item => {
    if (item?.id) return item
    if (inputedSkuItems.value.length) {
      const inputedItem = inputedSkuItems.value.find(inputedItem => inputedItem?.product_sku_id === item?.product_sku_id)
      if (inputedItem) {
        if (inputedItem?.id && !item?.id) delete inputedItem?.id
        return {...inputedItem}
      }
    }
    return item
  })
  totalStockCheckSkus.value = data.value?.total
  tableLoading.value = false
}

const openDialog = (item = null) => {
  isDialogVisible.value = true
  if (item) {
    getDataStockCheckSkus(item)
  }
}

const setInputedSkuItems = (newListSkus) => {
  const tempInputedMap = new Map(inputedSkuItems.value.map(item => [item.product_sku_id, item]))
  for (const skuItem of newListSkus) {
    if (skuItem?.quantity_real) {
      tempInputedMap.set(skuItem.product_sku_id, skuItem)
    } else { 
      const hasItemIsInputed = tempInputedMap.has(skuItem?.product_sku_id)
      if (hasItemIsInputed) {
        tempInputedMap.delete(skuItem.product_sku_id)
      }
    }
  }
  inputedSkuItems.value = Array.from(tempInputedMap.values())
}

const quantityRealRules = () => {
  return (value) => {
    if (parseInt(value) <= 0) return 'Must be greater than 0'
    return true
  }
}

const reloadTable = async v => {
  tableLoading.value = true
  await updateOptions(v)
  await getDataStockCheckSkus(props.modelValue)
  tableLoading.value = false
}

const isValidData = async () => {
  const { valid: isValid } = await refForm.value?.validate()
  if (!isValid) {
    return false
  }
  if (!isHasOneSku.value) {
    toast.message = 'At least one SKU item filled!'
    toast.color = 'error'
    return false
  }
  return true
}

const openConfirmDialog = (type) => {
  confirmDialog.show = true
  confirmDialog.type = type
}

const confirm = async () => {
  confirmDialog.type === 'ok' ? await approve() : await cancel()
  confirmDialog.show = false
}

const onSubmit = async (isRequestApproval = false) => {
  if (!isValidData()) {
    return
  }
  toast.loading = true
  const data = stockCheckSkus.value
    .filter(item => item.quantity_real)
    .map(({ product_sku_name, ...rest }) => rest)
  const { error } = await useApi(`/factory/stock-checks/${props.modelValue?.id}/skus`, { body: data, method: 'POST' })

  toast.message = error.value?.data?.message ?? null
  toast.color = error.value?.data?.message ? 'error' : 'success'
  toast.loading = false

  if (!toast.message) {
    if (isRequestApproval) {
      await requestApproval()
    }
    emit('change')
    onReset()
  }
}

async function approve() {
  if (!isValidData()) {
    return
  }
  toast.loading = true
  const data = stockCheckSkus.value
    .filter(item => item.quantity_real)
    .map(({ product_sku_name, ...rest }) => rest)
  const { error } = await useApi(`/factory/stock-checks/${props.modelValue?.id}/approve`, { body: data, method: 'PUT' })

  toast.message = error.value?.data?.message ?? null
  toast.color = error.value?.data?.message ? 'error' : 'success'
  toast.loading = false

  if (!toast.message) {
    emit('update:isApprove', false)
    emit('change')
    onReset()
  }
}

async function cancel() {
  toast.loading = true
  const { error } = await useApi(`/factory/stock-checks/${props.modelValue?.id}`, {
    body: { status: STOCK_CHECK_STATUS.CANCEL_APPROVAL },
    method: 'PUT',
  })
  toast.message = error.value?.data?.message ?? null
  toast.color = error.value?.data?.message ? 'error' : 'success'
  toast.loading = false
  if (!toast.message) {
    emit('update:isApprove', false)
    emit('change')
    onReset()
  }
}

async function requestApproval() {
  toast.loading = true
  const body = {status: STOCK_CHECK_STATUS.WAITING_APPROVAL}
  const { error } = await useApi(`/factory/stock-checks/${props.modelValue?.id}`, { body, method: "PUT" })

  toast.message = error.value?.data?.message ?? null
  toast.color = error.value?.data?.message ? 'error' : 'success'
  toast.loading = false
}

function onReset() {
  isDialogVisible.value = false
  isResetForm.value = true
  stockCheckSkus.value = []
  totalStockCheckSkus.value = 0
  emit('update:isApprove', false)
  nextTick(() => {
    refForm.value?.resetValidation()
    isResetForm.value = false
  })
}

watch([() => stockCheckSkus.value, () => filter], ([newListSkus, newFilter]) => {
  if (newListSkus) {
    setInputedSkuItems(newListSkus)
  }
  if (newFilter?.limit > props.totalStockCheckSkus) {
    filter.page = 1
    paginationKey.value++
  }
}, { deep: true })

defineExpose({
  openDialog,
})
</script>
<template>
  <VDialog
    :model-value="isDialogVisible"
    @update:model-value="onReset"
  >
    <!-- 👉 dialog close btn -->
    <DialogCloseBtn @click="onReset" />

    <VCard>
      <VCardItem>
        <VCardTitle class="text-h4 text-center">
          Checking SKUs
        </VCardTitle>
      </VCardItem>
      <VCardText>
        <VForm ref="refForm">
          <VRow>
            <VCol cols="12">
              <div class="d-flex flex-wrap py-4 gap-4">
                <div class="me-3 d-flex gap-3 d-fa-c">
                  <AppItemPerPage v-model="filter.limit" :is-reset-form="isResetForm" />
                  <span>
                    {{ totalStockCheckSkus }} SKU
                  </span>
                </div>
              </div>
              <VDivider />
              <VInput class="input-table">
                <VDataTableServer
                  v-model:items-per-page="filter.limit"
                  v-model:page="filter.page"
                  :items="stockCheckSkus"
                  :items-length="totalStockCheckSkus"
                  :headers="headers"
                  :loading="tableLoading"
                  class="text-no-wrap"
                  @update:options="v => reloadTable(v)"
                >
                  <template #item.name="{item}">
                    {{ item.product_sku_name }}
                    <sup v-if="item.id" style="color: #ff3131;">Added</sup>
                  </template>
                  <template #item.quantity_expect="{ item }">
                    <span>{{ item.quantity_expect }}</span>
                  </template>
                  <template #item.quantity_real="{ item }">
                    <div class="py-2">
                      <AppTextField
                        v-model="item.quantity_real"
                        :rules="[quantityRealRules()]"
                        clearable
                        clear-icon="tabler-xbox-x"
                        density="compact"
                        placeholder="Type quantity real (*)"
                        type="number"
                      />
                    </div>
                  </template>
                  <template #item.quantity_diff="{ item }">
                    {{ formatNumber(quantityDiff(item)) }}
                  </template>
                  <template #item.note="{ item }">
                    <AppTextField v-model="item.note" density="compact" placeholder="Note..." />
                  </template>
                  <!-- pagination -->
                  <template #bottom>
                    <VDivider />
                    <AppPagination :key="paginationKey" v-model="filter.page" :total="totalStockCheckSkus" :items-per-page="filter.limit" />
                  </template>
                </VDataTableServer>
              </VInput>
            </VCol>
            <VCol
              cols="12"
              class="text-center"
            >
              <VBtn
                v-if="!isApprove"
                :loading="toast.loading"
                class="me-2"
                @click="onSubmit()"
              >
                Save
              </VBtn>
              <VBtn
                v-if="!isApprove && isCheckingStatus"
                :loading="toast.loading"
                color="info"
                @click="onSubmit(isRequestApproval = true)"
              >
                Request Approval
              </VBtn>
              <VBtn
                v-if="isApprove"
                :loading="toast.loading"
                class="me-2"
                color="success"
                @click="openConfirmDialog('ok')"
              >
                Approve
              </VBtn>
              <VBtn
                v-if="isApprove"
                :loading="toast.loading"
                color="warning"
                @click="openConfirmDialog('cancel')"
              >
                Cancel
              </VBtn>
            </VCol>
          </VRow>
        </VForm>
      </VCardText>
    </VCard>
  </VDialog>
  <!-- 👉 Confirm Dialog -->
  <ConfirmDialog
    v-if="confirmDialog.show"
    v-model:isDialogVisible="confirmDialog.show"
    :confirmType="confirmDialog.type"
    @confirm="confirm"
  />
  <VSnackbar
    v-if="toast.message"
    v-model="toast.message"
    vertical
    :color="toast.color"
    :timeout="2000"
  >
    {{ toast.message }}
  </VSnackbar>
</template>

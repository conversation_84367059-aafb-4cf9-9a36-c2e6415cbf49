<script setup>
import useFilter from '@/composables/useFilter'
import EditSKUsStockBatchTable from '~/views/pages/factories/stock-batches/EditSKUsStockBatchTable.vue'
import { VDateInput } from 'vuetify/labs/VDateInput'
import { PRODUCT_SERVICE, STOCK_BATCH_TYPE, STOCK_BATCH_TYPE_OPTIONS } from '@helpers/ConstantHelper.js'

const props = defineProps({
  modelValue: {
    type: Object,
    required: false,
    default: undefined,
  }
})

const { filter, updateOptions } = useFilter({
  page: 1,
  limit: 25,
})

const { filter: filterSkus, updateOptions: updateOptionSkus } = useFilter({
  page: 1,
  limit: 25,
})

const emit = defineEmits([
  'update',
  'update:modelValue',
])

const headers = ref([
  {
    title: 'Sku name',
    key: 'name',
    width: '20%',
  },
  {
    title: 'Add quantity',
    key: 'quantity',
    width: '15%',
    sortable: false
  },
  {
    title: 'Add price',
    key: 'unit_price',
    width: '15%',
    sortable: false
  },
  {
    title: 'Reason',
    key: 'reason',
    width: '30%',
    sortable: false
  },
  {
    title: 'Total Price',
    key: 'price',
    sortable: false
  },
].filter(Boolean))

const isDialogVisible = ref(false)
const cardRef = ref()

const isTypeImport = computed(() => form.type ? form.type === STOCK_BATCH_TYPE.IMPORT : props.modelValue?.type === STOCK_BATCH_TYPE.IMPORT)
const isTypeTransfer = computed(() => form.type ? form.type === STOCK_BATCH_TYPE.TRANSFER : props.modelValue?.type === STOCK_BATCH_TYPE.TRANSFER)

const isDisplayTransferStockSelect = computed(() => (form.stock_id || props.modelValue?.stock.id) && isTypeTransfer.value)

const productSkus = ref([])
const totalProductSkus = ref(0)
const inputedSkuItemsImport = ref([])

const skusForExport = ref([])
const totalSkusForExport = ref(0)
const inputedSkuItemsExport = ref([])

const tableLoading = ref(false)
const totalQuantity = ref(0);
const totalPrice = ref(0);

const refForm = ref()
const loading = reactive({
  status: false,
  message: null,
})

const form = reactive({
  stock_id: props.modelValue?.stock?.id,
  name: props.modelValue?.name,
  type: props.modelValue?.type,
  transfer_to_stock_id: props.modelValue?.transfer_to_stock_id,
  status: props.modelValue?.status,
  handle_date: props.modelValue?.handle_date,
  note: props.modelValue?.note,
  photos: props.modelValue?.photos,
  reason: props.modelValue?.reason
})

const validateErrors = ref(null)
const isResetForm = ref(false)

const confirmDialog = reactive({
  show: false,
  type: "ok",
})

const onReset = () => {
  isDialogVisible.value = false
  isResetForm.value = true
  productSkus.value = []
  skusForExport.value = []
  totalProductSkus.value = 0
  totalSkusForExport.value = 0
  totalQuantity.value = 0
  totalPrice.value = 0
  // Reset inputed items to prevent data carry-over between different stock batches
  inputedSkuItemsImport.value = []
  inputedSkuItemsExport.value = []
  validateErrors.value = null
  emit('update:modelValue', null)
  nextTick(() => {
    refForm.value?.reset()
    refForm.value?.resetValidation()
    isResetForm.value = false
  })
}

const getInputtedSkusForUpdate = async (stockBatchId) => {
  if (!stockBatchId) return
  const params = {
    type: form.type,
    stock: form.stock_id,
  }
  const { data } = await useApi(`/factory/stock-batches/${stockBatchId}/inputted-skus`, { params })
  if (isTypeImport.value) {
    inputedSkuItemsImport.value = data.value?.data
  } else {
    inputedSkuItemsExport.value = data.value?.data
  }
}

const getSkusForUpdate = async (newModel) => {
  if (!newModel?.id) return
  const stockBatchId = newModel.id
  const params = isTypeImport.value ? filter : filterSkus
  params.type = form.type
  params.stock = form.stock_id

  tableLoading.value = true
  const { data } = await useApi(`/factory/stock-batches/${stockBatchId}/skus`, { params })
  if (isTypeImport.value) {
    productSkus.value = data.value?.data.map(item => {
      if (item?.id) return item
      if (inputedSkuItemsImport.value.length) {
        const inputedItem = inputedSkuItemsImport.value.find(inputedItem => inputedItem?.product_sku_id === item?.product_sku_id)
        if (inputedItem) {
          if (inputedItem?.id && !item?.id) delete inputedItem?.id
          return {...inputedItem}
        }
      }
      return item
    })
    totalProductSkus.value = data.value?.total
  } else {
    skusForExport.value = data.value?.data.map(item => {
      if (item?.id) return item
      if (inputedSkuItemsExport.value.length) {
        const inputedItem = inputedSkuItemsExport.value.find(inputedItem => inputedItem?.product_sku_id === item?.product_sku_id)
        if (inputedItem) {
          if (inputedItem?.id && !item?.id) delete inputedItem?.id
          return {...inputedItem}
        }
      }
      return item
    })
    totalSkusForExport.value = data.value?.total
  }
  
  totalQuantity.value = newModel?.quantity
  totalPrice.value = newModel?.price
  tableLoading.value = false
}

const openDialog = async (stockBatchItem = null) => {
  isDialogVisible.value = true
  form.stock_id = stockBatchItem?.stock.id
  form.name = stockBatchItem?.name
  form.type = stockBatchItem?.type
  form.transfer_to_stock_id = stockBatchItem?.transfer_to_stock_id,
  form.status = stockBatchItem?.status
  form.handle_date = stockBatchItem?.handle_date
  form.note = stockBatchItem?.note
  form.photos = stockBatchItem?.photos
  form.reason = stockBatchItem?.reason
  await getInputtedSkusForUpdate(stockBatchItem.id)
  await getSkusForUpdate(stockBatchItem)
}

const searchSkus = async () => {
  if (!filter?.product_id) {
    delete filter.att1_value
    delete filter.att2_value
  }
  if (!filterSkus?.product_id) {
    delete filterSkus.att1_value
    delete filterSkus.att2_value
  }
  getSkusForUpdate(props.modelValue)
}


const changeSkusDataForStock = async (newStock) => {
  if (newStock === form.transfer_to_stock_id) {
    form.transfer_to_stock_id = null
  }
  if (!(form.type === STOCK_BATCH_TYPE.IMPORT && productSkus.value.length)) {
    getSkusForUpdate(props.modelValue)
  }
}

const changeSkusDataForType = async (newType) => {
  if (form.stock_id) {
    getSkusForUpdate(props.modelValue)
  }
}

const openConfirmDialog = (type) => {
  confirmDialog.show = true
  confirmDialog.type = type
}

const confirm = async () => {
  confirmDialog.type === 'ok' ? await onSubmit() : await cancelStockBatch()
  confirmDialog.show = false
}

async function onSubmit() {
  const { valid: isValid, errors } = await refForm.value?.validate()
  if (!isValid) {
    cardRef?.value?.$el.scrollTo({ top: 0, behavior: 'smooth' });
    return
  }
  loading.status = true
  validateErrors.value = null

  const skus = isTypeImport.value ? inputedSkuItemsImport.value : inputedSkuItemsExport.value
  
  let data = {}
  data.stock_batch = Object.assign({}, form)
  data.stock_batch.price = totalPrice.value
  data.stock_batch.quantity = totalQuantity.value

  let dataSkus = []
  for (const skuItem of skus) {
    if (skuItem?.quantity && skuItem?.unit_price) {
      const item = Object.assign({}, {
        ...skuItem,
        quantity_final: skuItem.quantity
      })
      dataSkus.push(item)
    }
  }
  data.stock_batch_skus = [...dataSkus]

  const { error } = await useApi(`/factory/stock-batches/${props.modelValue?.id}/approve`, {
    body: data,
    method: 'PUT',
  })

  if (error?.value?.statusCode === 400) {
    validateErrors.value = error?.value?.data?.data
  }

  loading.message = error?.value?.data?.message ?? null
  loading.color = error?.value?.data?.message ? 'error' : 'success'
  loading.status = false

  if (cardRef.value && cardRef.value.$el) {
    cardRef.value.$el.scrollTo({ top: 0, behavior: 'smooth' });
  }
  if (!loading.message) {
    emit('update')
    onReset()
  }
}

async function cancelStockBatch() {
  loading.status = true
  const { error } = await useApi(`/factory/stock-batches/${props.modelValue?.id}/cancel`, {
    method: 'PUT',
  })
  loading.message = error?.value?.data?.message ?? null
  loading.color = error?.value?.data?.message ? 'error' : 'success'
  loading.status = false
  if (!loading.message) {
    emit('update')
    onReset()
  }
}

defineExpose({
  openDialog,
})
</script>
<template>
  <VDialog
    :model-value="isDialogVisible"
    @update:model-value="onReset"
  >
    <!-- 👉 Dialog close btn -->
    <DialogCloseBtn @click="onReset" />

    <VCard ref="cardRef" class="pa-sm-8 pa-5">
      <!-- 👉 Title -->
      <VCardItem class="text-center pt-0">
        <VCardTitle class="text-h3">
          Approve Batch to Stock
        </VCardTitle>
      </VCardItem>

      <VCardText class="px-0">
        <AppValidateErrors v-if="validateErrors" :error-messages="validateErrors" />
        <!-- 👉 Form -->
        <VForm ref="refForm" @submit.prevent="onSubmit">
          <VRow>
            <VCol cols="12" sm="3" :class="$vuetify.display.smAndUp ? 'pb-0' : ''">
              <FactoryStockSelectInput
                v-model="form.stock_id"
                :disabled="tableLoading"
                :rules="[requiredValidator]"
                label="Stock (*)"
                placeholder="Select Stock"
                @update:modelValue="changeSkusDataForStock"
              />
            </VCol>
            <VCol cols="12" sm="3" :class="$vuetify.display.smAndUp ? 'pb-0' : ''">
              <AppSelect
                v-model="form.type"
                :items="STOCK_BATCH_TYPE_OPTIONS"
                :disabled="tableLoading"
                :rules="[requiredValidator]"
                label="Type (*)"
                placeholder="Select Type"
                clearable
                clear-icon="tabler-x"
                @update:model-value="changeSkusDataForType"
              />
            </VCol>
            <VCol v-if="isDisplayTransferStockSelect" cols="12" sm="3" :class="$vuetify.display.smAndUp ? 'pb-0' : ''">
              <StockSelectInput
                v-model="form.transfer_to_stock_id"
                density="compact"
                label="Transfer Stock (*)"
                placeholder="Select Transfer Stock"
                :disabled-item="form?.stock_id"
                :rules="[requiredValidator]"
              />
            </VCol>
            <VCol cols="12" sm="3" :class="$vuetify.display.smAndUp ? 'pb-0' : ''">
              <AppTextField
                v-model="form.name"
                label="Name (*)"
                placeholder="Type name"
                :rules="[requiredValidator]"
              />
            </VCol>
            <VCol cols="12" sm="3" :class="$vuetify.display.smAndUp ? 'pb-0' : ''">
              <VLabel class="mb-1 text-body-2 text-high-emphasis">
                Handle date (*)
              </VLabel>
              <VDateInput
                v-model="form.handle_date"
                clearable
                density="compact"
                placeholder="Select Date"
                :rules="[requiredValidator]"
              ></VDateInput>
            </VCol>
            <VCol cols="12" sm="3" :class="$vuetify.display.smAndUp ? 'pb-0' : ''">
              <DFileInput
                v-model="form.photos"
                accept="image/*"
                label="Photos"
                placeholder="Enter photos"
                :selected="form.primary"
                @update:select-value="form.primary = $event"
              />
            </VCol>
            <VCol cols="12" sm="4" :class="$vuetify.display.smAndUp ? 'pb-0' : ''">
              <AppTextarea
                v-model="form.note"
                label="Note"
                auto-grow
                placeholder="Type note"
              />
            </VCol>
            <VCol cols="12">
              <VLabel class="mb-1 text-body-2 text-high-emphasis">
                Select Skus
              </VLabel>
              <VRow class="py-4 pt-0">
                <VCol cols="12" sm="3" class="d-flex d-fa-c">
                  <div class="d-flex gap-3 d-fa-c">
                    <AppItemPerPage v-if="isTypeImport" v-model="filter.limit" :is-reset-form="isResetForm" />
                    <AppItemPerPage v-else v-model="filterSkus.limit" :is-reset-form="isResetForm" />
                    <span>
                      {{ isTypeImport ? totalProductSkus : totalSkusForExport }} Skus
                    </span>
                  </div>
                </VCol>
                <VCol cols="12" sm="9">
                  <VRow dense justify="end">
                    <template v-if="isTypeImport">
                      <VCol cols="12" sm="4">
                        <ProductSelectInput
                          v-model="filter.product_id"
                          clearable
                          density="compact"
                          label="Filter Product"
                          placeholder="Select Product"
                          :service="PRODUCT_SERVICE.PRODUCTION"
                          @update:modelValue="searchSkus"
                        />
                      </VCol>
                      <VCol cols="12" sm="8">
                        <ProductSkuSelectInput
                          v-model:filter="filter"
                          :att1-attrs="{
                            clearable: true,
                          }"
                          :att2-attrs="{
                            clearable: true,
                          }"
                          :productId="filter.product_id"
                          @change="searchSkus"
                        />
                      </VCol>
                    </template>
                    <template v-else>
                      <VCol cols="12" sm="4">
                        <ProductSelectInput
                          v-model="filterSkus.product_id"
                          density="compact"
                          label="Filter Product"
                          placeholder="Select Product"
                          :service="PRODUCT_SERVICE.PRODUCTION"
                          @update:modelValue="searchSkus"
                        />
                      </VCol>
                      <VCol cols="12" sm="8">
                        <ProductSkuSelectInput
                          v-model:filter="filterSkus"
                          :att1-attrs="{
                            clearable: true,
                          }"
                          :att2-attrs="{
                            clearable: true,
                          }"
                          :productId="filterSkus.product_id"
                          @change="searchSkus"
                        />
                      </VCol>
                    </template>
                  </VRow>
                </VCol>
              </VRow>

              <VDivider />

              <div>
                <!-- Table data for type import -->
                <template v-if="isTypeImport">
                  <EditSKUsStockBatchTable
                    v-model:items="productSkus"
                    v-model:inputedItems="inputedSkuItemsImport"
                    v-model:filter="filter"
                    v-model:totalQuantity="totalQuantity"
                    v-model:totalPrice="totalPrice"
                    :headers="headers"
                    :items-length="totalProductSkus"
                    :loading="tableLoading"
                    @updateOptions="async ($e) => { await updateOptions($e); getSkusForUpdate(modelValue) }"
                  />
                </template>
                <!-- Table data for type export and transfer -->
                <template v-else>
                  <EditSKUsStockBatchTable
                    v-model:items="skusForExport"
                    v-model:inputedItems="inputedSkuItemsExport"
                    v-model:filter="filterSkus"
                    v-model:totalQuantity="totalQuantity"
                    v-model:totalPrice="totalPrice"
                    :items-length="totalSkusForExport"
                    :stock-batch-type="form.type"
                    :loading="tableLoading"
                    @updateOptions="async ($e) => { await updateOptionSkus($e); getSkusForUpdate(modelValue) }"
                  />
                </template>
              </div>
            </VCol>
            <VCol
              cols="12"
              class="text-center"
            >
              <VBtn
                :loading="loading.status"
                color="success"
                @click="openConfirmDialog('ok')"
              >
                Approve
              </VBtn>
              <VBtn
                :loading="loading.status"
                class="ml-2"
                color="warning"
                @click="openConfirmDialog('cancel')"
              >
                Cancel
              </VBtn>
            </VCol>
          </VRow>
        </VForm>
      </VCardText>
    </VCard>
  </VDialog>
  <!-- 👉 Confirm Dialog -->
  <ConfirmDialog
    v-if="confirmDialog.show"
    v-model:isDialogVisible="confirmDialog.show"
    :confirmType="confirmDialog.type"
    @confirm="confirm"
  />
  <VSnackbar
    v-model="loading.message"
    vertical
    :color="loading.color" 
    :timeout="2000"
  >
    {{ loading.message }}
  </VSnackbar>
</template>

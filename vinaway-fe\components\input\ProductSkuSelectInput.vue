<script setup>
import { ref, watch, reactive, computed } from 'vue'
import { useApi } from '@/composables/useApi'

const props = defineProps({
  modelValue: {
    type: Number,
  },
  productId: {
    type: Number,
    default: null,
  },
  productSkuId: {
    type: Number,
    default: null,
  },
  returnObject: {
    type: Boolean,
    default: false,
  },
  filter: {
    type: Object,
    default: null,
  },
  att1Attrs: {
    type: Object,
    default: () => ({}),
  },
  att2Attrs: {
    type: Object,
    default: () => ({}),
  },
})

const emit = defineEmits([
  'update:modelValue',
  'change',
  'update:filter',
])

defineOptions({
  name: 'ProductSkuSelectInput',
  inheritAttrs: true,
})

const loading = ref(false)
const items = ref([])
const product = ref(null)
const form = reactive({})

const getProduct = async productId => {
  if (!productId) {
    product.value = null
    return
  }
  const { data } = await useApi(`products/${productId}`)
  product.value = data.value
}

let refresh = async productId => {
  if (!productId) {
    items.value = []
    return
  }

  const { data } = await useApi('/product-skus/options', {
    params: { "product_id": productId },
  })
  items.value = data.value ?? []
}

const att1Options = computed(() => {
  if (!items.value.length) {
    return []
  }
  const att1s = items.value.map(item => (item?.att1_value)).filter(Boolean)
  return att1s.length ? [...new Set(att1s)] : []
})

const att2Options = computed(() => {
  if (!items.value.length) {
    return []
  }
  const att2s = items.value.map(item => (item?.att2_value)).filter(Boolean)
  return att2s.length ? [...new Set(att2s)] : []
})

const attributeSelected = computed(() => items.value.find(item => (item.att1_value === form.att1 && item.att2_value === form.att2)))

const handleUpdateModelValueAtt1 = (value) => {
  if (props.filter) {
    props.filter.att1_value = value
    emit('change')
  }
}

const handleUpdateModelValueAtt2 = (value) => {
  if (props.filter) {
    props.filter.att2_value = value
    emit('change')
  }
}

watch([() => (props.productId), () => (props.productSkuId)], async ([productId, productSkuId]) => {
  await getProduct(productId)
  await refresh(productId)
  if (productSkuId) {
    const item = items.value.find(item => item.id === productSkuId)
    form.att1 = item?.att1_value
    form.att2 = item?.att2_value
    handleUpdateModelValueAtt1(item?.att1_value)
    handleUpdateModelValueAtt2(item?.att2_value)
  }
}, { immediate: true })

watch(() => attributeSelected.value, newVal => {
  emit('update:modelValue', props.returnObject ? newVal : newVal?.id)
})

// onUnmounted hook removed - cannot mutate props
</script>

<template>
  <VRow dense>
    <VCol cols="12" sm="6" v-if="att1Options?.length">
      <div class="mb-1" style="font-size: 12px">
        {{ product?.att1_name ?? '' }}
      </div>
      <VAutocomplete
        v-bind="att1Attrs"
        v-model="form.att1"
        :disabled="!att1Options?.length"
        :loading="loading"
        :items="att1Options"
        :return-object="returnObject"
        :placeholder="att1Attrs?.placeholder ?? `Search or select ${product?.att1_name ?? ''}`"
        @update:modelValue="handleUpdateModelValueAtt1"
      />
    </VCol>
    <VCol cols="12" sm="6" v-if="att2Options?.length">
      <div class="mb-1" style="font-size: 12px">
        {{ product?.att2_name ?? '' }}
      </div>
      <VAutocomplete
        v-bind="att2Attrs"
        v-model="form.att2"
        :disabled="!att2Options?.length"
        :loading="loading"
        :items="att2Options"
        :return-object="returnObject"
        :placeholder="att2Attrs?.placeholder ?? `Search or select ${product?.att2_name ?? ''}`"
        @update:modelValue="handleUpdateModelValueAtt2"
      />
    </VCol>
  </VRow>
</template>

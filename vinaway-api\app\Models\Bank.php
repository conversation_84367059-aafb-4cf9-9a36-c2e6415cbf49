<?php

namespace App\Models;

use App\Traits\CreatorRelationship;
use App\Traits\Filterable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Bank extends Model
{
    use HasFactory, SoftDeletes, Filterable, CreatorRelationship;

    const TYPE_PINGPONG = 1;
    const TYPE_LIANLIAN = 2;
    const TYPE_PAYONEER = 3;
    const TYPE_BANK_VN = 4;
    const TYPE_PAYPAL = 5;
    const TYPE_WORLDFIRST = 6;
    const CURRENCY_USD = 1;
    const CURRENCY_VND = 2;
    const ALLOW_TOPUP = 1;

    protected $table = 'banks';
    protected $fillable = [
        'name',
        'type',
        'currency',
        'account_holder',
        'bank_name',
        'abbr_bank_name',
        'account_number',
        'qr_code',
        'balance',
        'balance_ref',
        'allow_topup_flag',
        'creator_id',
        'updater_id',
        'deleter_id',
    ];

    protected $filter = [
        'name',
        'balance',
        'balance_ref',
    ];

    public function filterQuery($query, $value)
    {
        if (!$value) {
            return $query;
        }
        return $query->where(function ($query) use ($value) {
            return $query->where('name', 'LIKE', "%$value%")
                ->orWhere('balance', 'LIKE', "%$value%")
                ->orWhere('balance_ref', 'LIKE', "%$value%");
        });
    }

    public function filterType($query, $value)
    {
        if (empty($value)) {
            return $query;
        }

        return $query->where('type', $value);
    }

    public function filterCurrency($query, $value)
    {
        if (empty($value)) {
            return $query;
        }

        return $query->where('currency', $value);
    }

    public function filterAllowTopupFlag($query, $value)
    {
        if (is_null($value)) {
            return $query;
        }

        return $query->where('allow_topup_flag', $value);
    }

    public static function getBankTypeText($type)
    {
        return match ($type) {
            self::TYPE_PINGPONG => 'Pingpong',
            self::TYPE_LIANLIAN => 'Lianlian',
            self::TYPE_PAYONEER => 'Payoneer',
            self::TYPE_BANK_VN => 'Bank VN',
            self::TYPE_PAYPAL => 'Paypal',
            self::TYPE_WORLDFIRST => 'WorldFirst',
            default => 'Unknown',
        };
    }

    public static function getCurrencyText($type)
    {
        return match ($type) {
            self::CURRENCY_USD => 'USD',
            self::CURRENCY_VND => 'VND',
            default => 'Unknown',
        };
    }
}

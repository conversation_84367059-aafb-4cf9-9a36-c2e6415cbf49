<?php

namespace App\Services\StockSkus;

use App\Http\Resources\StockSkuResource;
use App\Models\ProductSku;
use App\Repositories\StockSkuRepository;
use App\Services\BaseAPIService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class StockSkuService extends BaseAPIService
{
    protected $storeFields = [
        'stock_id',
        'product_id',
        'product_sku_id',
        'quantity',
    ];
    protected $updateFields = [
        'stock_id',
        'product_id',
        'product_sku_id',
        'quantity',
    ];

    public function __construct()
    {
        parent::__construct();
        $this->repo = app(StockSkuRepository::class);
    }

    public function store($input, $user)
    {
        try {
            DB::beginTransaction();
            $stockSkus = [];
            foreach ($input as $item) {
                $cond = collect($item)->only(['stock_id', 'product_id', 'product_sku_id'])->toArray();
                $stockSku = $this->repo->newQuery()
                    ->where($cond)
                    ->first();
                if (!$stockSku) {
                    $stockSkus[] = parent::store($item, $user);
                } else {
                    $params = [
                        'quantity' => $stockSku->quantity + $item['quantity'],
                    ];
                    if ($this->hasCreator) {
                        $params['updater_id'] = $user->id;
                    }
                    $this->repo->newQuery()->where($cond)->update($params);
                }
            }
            DB::commit();
            return $stockSkus;
        } catch (\Exception $e) {
            DB::rollBack();
            return false;
        }
    }

    public function paginate($search, $page, $perPage, $columns = ['*'], $sortBy = 'id', $orderBy = 'desc'): array
    {
        $data = $this->repo->getPaginateStockSkusWithStats($search, $page, $perPage, $columns, $sortBy, $orderBy);
        $total = $data->total();
        $items = $data->items();
        return [
            'total' => $total,
            'data' => StockSkuResource::collection($items)
        ];
    }

    public function paginateStockSkus(Request $request)
    {
        $search = $request->except(['page', 'limit', 'sortBy', 'orderBy']);
        $sortBy = $request->get('sortBy', 'id');
        $orderBy = $request->get('orderBy', 'desc');
        $page = $request->get('page', 1);
        $limit = $request->get('limit', 10);
        $columns = ['*'];

        $query = $this->repo->allQuery($search);
        $query->with(['productSku']);

        if (!empty($sortBy) && !empty($orderBy)) {
            if ($sortBy == 'quantity') {
                $query->orderBy($sortBy, $orderBy);
            } else {
                $query->join(
                    'product_skus',
                    'product_skus.id', '=', 'stock_skus.product_sku_id'
                )->orderBy('product_skus.' . $sortBy, $orderBy)
                ->select('stock_skus.*');
            }
        }

        $data = $query->paginate($limit, $columns, 'page', $page);
        $total = $data->total();
        $items = $data->items();
        return [
            'total' => $total,
            'data' => StockSkuResource::collection($items)
        ];
    }

    public function paginateStockSkusForUpdateBatch(Request $request, $stockBatchId)
    {
        $search = $request->except(['page', 'limit', 'sortBy', 'orderBy']);
        $sortBy = $request->get('sortBy', 'id');
        $orderBy = $request->get('orderBy', 'desc');
        $page = $request->get('page', 1);
        $limit = $request->get('limit', 10);
        $columns = [
            'stock_batch_skus.id',
            'stock_skus.stock_id',
            'stock_skus.product_id',
            'stock_skus.product_sku_id',
            DB::raw('COALESCE(`stock_batch_skus`.`product_sku_name`, `product_skus`.`name`) AS `product_sku_name`'),
            'stock_batch_skus.reason',
            'stock_skus.quantity AS stock_sku_quantity',
            'stock_batch_skus.quantity',
            'stock_batch_skus.unit_price'
        ];

        return $this->repo->getPaginateStockSkusForUpdateBatch($search, $page, $limit, $columns, $sortBy, $orderBy, $stockBatchId);

    }

    public function paginateStockSkusForStockCheck(Request $request, $stockCheckId)
    {
        $search = $request->except(['page', 'limit', 'sortBy', 'orderBy']);
        $sortBy = $request->get('sortBy', 'id');
        $orderBy = $request->get('orderBy', 'desc');
        $page = $request->get('page', 1);
        $limit = $request->get('limit', 10);
        $columns = [
            'stock_check_skus.id',
            'stock_skus.id AS stock_sku_id',
            'stock_skus.product_id',
            'stock_skus.product_sku_id',
            'product_skus.name AS product_sku_name',
            'stock_skus.quantity AS quantity_expect',
            DB::raw("COALESCE(stock_check_skus.quantity_real, '') AS quantity_real"),
            DB::raw("COALESCE(stock_check_skus.note, '') AS note"),
        ];

        return $this->repo->getPaginateStockSkusForStockCheck($search, $page, $limit, $columns, $sortBy, $orderBy, $stockCheckId);
    }

    public function getStockSku($conditions)
    {
        return $this->repo->newQuery()
            ->where($conditions)
            ->first();
    }
}

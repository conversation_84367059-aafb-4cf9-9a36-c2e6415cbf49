<?php

namespace App\Models;

use App\Traits\CreatorRelationship;
use App\Traits\Filterable;
use App\Traits\FilterStaffFactory;
use App\Traits\SavingCreatorUpdaterDeleterId;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class StockCheck extends Model
{
    use HasFactory, SoftDeletes, Filterable, CreatorRelationship, SavingCreatorUpdaterDeleterId, FilterStaffFactory;

    protected $table = "stock_checks";

    protected $fillable = [
        'stock_id',
        'name',
        'check_date',
        'status',
        'note',
        'approve_staff_id',
        'approve_time',
        'creator_id',
        'updater_id',
        'deleter_id',
    ];

    protected $filter = [
        'stock_id',
        'name',
        'check_date',
        'status',
        'approve_staff_id',
        'approve_time',
        'creator_id',
    ];

    const STATUS_INIT = 0;
    const STATUS_IN_PROGRESS = 1;
    const STATUS_WAITING_APPROVAL = 2;
    const STATUS_APPROVED = 3;
    const STATUS_CANCEL_APPROVAL = 4;

    public function filterQuery($query, $value)
    {
        if (!$value) {
            return $query;
        }
        return $query->where('name', 'like', "%$value%");
    }

    /**
     * Get the stock that owns the StockCheck
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function stock()
    {
        return $this->belongsTo(Stock::class, 'stock_id');
    }

    /**
     * Get all of the stockCheckSkus for the StockCheck
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function stockCheckSkus()
    {
        return $this->hasMany(StockCheckSku::class, 'stock_check_id');
    }
}

<script setup>
import { STOCK_BATCH_TYPE } from '@/helpers/ConstantHelper'
import { formatNumber } from '@/helpers/Helper'

const props = defineProps({
  headers: {
    type: Array,
    required: false,
    default: []
  },
  items: {
    type: Array,
    required: true,
  },
  inputedItems: {
    type: Array,
    default: () => []
  },
  filter: {
    type: Object,
    required: false,
  },
  itemsLength: {
    type: Number,
    required: true,
  },
  totalQuantity: Number | String,
  totalPrice: Number | String,
  stockBatchType: {
    type: Number,
    required: false,
    default() {
      return STOCK_BATCH_TYPE.IMPORT
    }
  },
  loading: {
    type: Boolean,
    required: false
  },
  readonly: {
    type: Boolean,
    required: false,
    default: false
  }
})

const emit = defineEmits([
  'update:inputedItems',
  'update:totalQuantity',
  'update:totalPrice',
  'updateOptions'
])

const isTypeImport = computed(() => props.stockBatchType === STOCK_BATCH_TYPE.IMPORT)
const isTypeExport = computed(() => props.stockBatchType === STOCK_BATCH_TYPE.EXPORT)
const isTypeTransfer = computed(() => props.stockBatchType === STOCK_BATCH_TYPE.TRANSFER)

const headers = ref([...props.headers])
const tempInputedItems = ref([...props.inputedItems])
const paginationKey = ref(0)

const isHasOneSku = computed(() => tempInputedItems.value.some(item => item?.quantity && item?.unit_price))

const totalSkuPrice = computed(() => (item) => (item?.unit_price * item?.quantity) || 0)
const totalQuantity = computed({
  get: () => props.totalQuantity,
  set: (newValue) => emit('update:totalQuantity', newValue)
})
const totalPrice = computed({
  get: () => props.totalPrice,
  set: (newValue) => emit('update:totalPrice', newValue)
})

const updateOptions = ($e) => {
  emit('updateOptions', $e);
}

const setInputedItems = (newItems) => {
  const tempInputedMap = new Map(props.inputedItems?.map(item => [item.product_sku_id, item]))
  tempInputedItems.value = [...props.inputedItems]
  for (const skuItem of newItems) {
    // check if item in skus list (skuItem) has been inputted (in tempInputedMap)
    const hasItemIsInputed = tempInputedMap.has(skuItem?.product_sku_id)
    if (skuItem?.quantity || skuItem?.unit_price) {
      // if there is 1 of 2 values ​​quantity and unit_price of sku's item...
      tempInputedMap.set(skuItem.product_sku_id, skuItem) // ...update or add skuItem to tempInputedMap
    } else if (!skuItem?.quantity && !skuItem?.unit_price) { 
      // if both values ​​quantity and unit_price of sku's item are empty...
      if (hasItemIsInputed) {
        tempInputedMap.delete(skuItem.product_sku_id) // ...remove skuItem from tempInputedMap
      }
    }
  }
  tempInputedItems.value = Array.from(tempInputedMap.values())
  emit('update:inputedItems', tempInputedItems.value)
}

const requiredQuantityItem = (item) => {
  return (value) => {
    if (!value && item?.unit_price) return 'This field is required'
    if (parseInt(value) < 0) return 'Must be greater than 0'
    if (!isTypeImport.value && value > item?.quantity) return "Must be less than or equal to Quantity in Stock"
    return true
  }
}

const requiredPriceItem = (item) => {
  return (value) => {
    if (!value && item?.quantity) return 'This field is required'
    if (parseInt(value) < 0) return 'Must be greater than 0'
    return true
  }
}

const requiredHasOneItem = () => {
  if (!isHasOneSku.value) {
    return 'At least one sku item filled!'
  }
  return true
};

const calculateTotals = () => {
  totalQuantity.value = tempInputedItems.value.reduce((sum, item) => sum + (parseInt(item?.quantity) || 0), 0);
  totalPrice.value = tempInputedItems.value.reduce((sum, item) => sum + (parseInt(item?.unit_price) || 0) * (parseInt(item?.quantity) || 0), 0);
};

onUnmounted(() => {
  tempInputedItems.value = []
})

watch(() => props.stockBatchType, (newType) => {
  if (newType) {
    if (!isTypeImport.value) {
      headers.value = [
        {
          title: 'Sku name',
          key: 'name',
          width: '15%',
        },
        {
          title: 'Quantity in Stock',
          key: 'stock_sku_quantity',
          width: '5%',
          sortable: false
        },
        {
          title: isTypeExport.value ? 'Export quantity' : 'Transfer quantity',
          key: 'quantity',
          width: '15%',
          sortable: false
        },
        {
          title: isTypeExport.value ? 'Export price' : 'Transfer price',
          key: 'unit_price',
          width: '15%',
          sortable: false
        },
        {
          title: 'Reason',
          key: 'reason',
          width: '25%',
          sortable: false
        },
        {
          title: 'Total Price',
          key: 'price',
          sortable: false
        },
      ].filter(Boolean)
    }
  }
}, { immediate: true })

watch([() => props.items, () => props.filter], ([newItems, newFilter]) => {
  if (newItems) {
    setInputedItems(newItems)
    calculateTotals()
  }
  if (newFilter?.limit > props.itemsLength) {
    props.filter.page = 1
    paginationKey.value++
  }
}, { deep: true })
</script>
<template>
  <VInput class="input-table-edit" :rules="readonly ? [] : [requiredHasOneItem]">
    <VDataTableServer
      v-model:items-per-page="filter.limit"
      v-model:page="filter.page"
      :items="items"
      :items-length="itemsLength"
      :headers="headers"
      :loading="loading"
      hide-default-footer
      class="text-no-wrap"
      @update:options="updateOptions"
    >
      <template #header.stock_sku_quantity="{ column }">
        <p class="mb-0" style="text-wrap: wrap; width: 120px;">{{ column.title }}</p>
      </template>
      <template v-if="!isTypeImport" #item.stock_sku_quantity="{ item }">
        {{ item?.stock_sku_quantity }}
      </template>
      <template #item.name="{ item }">
        {{ item.product_sku_name }}
        <sup v-if="item.id" style="color: #ff3131;">Added</sup>
      </template>
      <template #item.quantity="{ item }">
        <div class="py-2">
          <AppTextField
            v-model="item.quantity"
            :clearable="!readonly"
            clear-icon="tabler-xbox-x"
            density="compact"
            placeholder="Type quantity (*)"
            type="number"
            :rules="readonly ? [] : [requiredQuantityItem(item)]"
            :readonly="readonly"
            @click:clear="calculateTotals"
            @input="calculateTotals" />
        </div>
      </template>
      <template #item.unit_price="{ item }">
        <div class="py-2">
          <AppTextField
            v-model="item.unit_price"
            :clearable="!readonly"
            clear-icon="tabler-xbox-x"
            density="compact"
            placeholder="Type price (*)"
            type="number"
            :rules="readonly ? [] : [requiredPriceItem(item)]"
            :readonly="readonly"
            @click:clear="calculateTotals"
            @input="calculateTotals" />
        </div>
      </template>
      <template #item.reason="{ item }">
        <AppTextField
          v-model="item.reason"
          density="compact"
          placeholder="Type reason"
          :readonly="readonly" />
      </template>
      <template #item.price="{ item }">
        <h3>{{ formatNumber(totalSkuPrice(item)) }}</h3>
      </template>
      <template #tfoot>
        <tfoot v-if="isTypeImport">
          <tr>
            <td></td>
            <td>
              <h2 class="text-left my-2">
                Total quantity<br />
                {{ formatNumber(totalQuantity) }}
              </h2>
            </td>
            <td></td>
            <td></td>
            <td>
              <h2 class="text-left my-2">
                Total price<br />
                {{ formatNumber(totalPrice) }}
              </h2>
            </td>
          </tr>
        </tfoot>
        <tfoot v-else>
          <tr>
            <td></td>
            <td></td>
            <td>
              <h2 class="text-left my-2">
                Total quantity<br />
                {{ formatNumber(totalQuantity) }}
              </h2>
            </td>
            <td></td>
            <td></td>
            <td>
              <h2 class="text-left my-2">
                Total price<br />
                {{ formatNumber(totalPrice) }}
              </h2>
            </td>
          </tr>
        </tfoot>
      </template>
      <!-- pagination -->
      <template #bottom>
        <VDivider />
        <AppPagination :key="paginationKey" v-model="filter.page" :total="itemsLength" :items-per-page="filter.limit" />
      </template>
    </VDataTableServer>
  </VInput>
</template>
<style lang="scss">
.input-table-edit {
  display: flex;
  flex-direction: column;

  & > .v-input__details {
    order: -1;

    .v-messages {
      font-size: 0.85rem;
      margin-bottom: 5px;
    }
  }
  .v-input__details {
    .v-messages {
      &__message {
        white-space: pre-wrap;
      }
    }
  }
}
</style>

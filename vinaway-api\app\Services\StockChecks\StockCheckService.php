<?php

namespace App\Services\StockChecks;

use App\Http\Resources\StockCheckResource;
use App\Models\StockCheck;
use App\Models\StockCheckSku;
use App\Repositories\StockCheckRepository;
use App\Repositories\StockCheckSkuRepository;
use App\Services\BaseAPIService;
use App\Services\StockSkus\StockSkuService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class StockCheckService extends BaseAPIService
{
    protected $storeFields = [
        'stock_id',
        'name',
        'check_date',
        'status',
        'note',
        'approve_staff_id',
        'approve_time',
    ];
    protected $updateFields = [
        'stock_id',
        'name',
        'check_date',
        'status',
        'note',
        'approve_staff_id',
        'approve_time',
    ];

    private $stockSkuService;
    private $stockCheckSkuRepo;

    public function __construct()
    {
        parent::__construct();
        $this->repo = app(StockCheckRepository::class);
        $this->stockSkuService = app(StockSkuService::class);
        $this->stockCheckSkuRepo = app(StockCheckSkuRepository::class);
    }

    public function paginate($search, $page, $perPage, $columns = ['*'], $sortBy = 'id', $orderBy = 'desc'): array
    {
        $query = $this->repo->allQuery($search);
        if (!empty($sortBy) && !empty($orderBy)) {
            $query->orderBy($sortBy, $orderBy);
        }
        $query->with(['stock', 'creator']);
        $data = $query->paginate($perPage, $columns, 'page', $page);
        return [
            'total' => $data->total(),
            'data' => StockCheckResource::collection($data->items())
        ];
    }

    public function paginateForRequest(Request $request): array
    {
        $search = array_merge([
            'staff_factory' => $request->user()->factory_id
        ], $request->except(['page', 'limit', 'sortBy', 'orderBy']));
        $sortBy = $request->get('sortBy', 'id');
        $orderBy = $request->get('orderBy', 'desc');
        $page = $request->get('page', 1);
        $limit = $request->get('limit', 10);
        $columns = ['*'];
        return $this->paginate($search, $page, $limit, $columns, $sortBy, $orderBy);

    }

    public function getStockCheckSkus(StockCheck $stockCheck, Request $request)
    {
        $request->merge(['stock' => $stockCheck->stock_id]);
        $paginateStockSkus = $this->stockSkuService->paginateStockSkusForStockCheck($request, $stockCheck->id);

        return [
            'total' => $paginateStockSkus->total(),
            'data' => $paginateStockSkus->items()
        ];
    }

    public function upsertStockCheckSkus(StockCheck $stockCheck, $data, $user, $isRequestApprove = false)
    {
        try {
            $dataSkuIds = collect($data)->pluck('id');
            $dataUpserts = array_map(function ($dataSku) use ($stockCheck, $user) {
                return array_merge($dataSku, [
                    'stock_check_id' => $stockCheck->id,
                    'quantity_diff' => $dataSku['quantity_real'] - $dataSku['quantity_expect'],
                ]);
            }, $data);
            DB::beginTransaction();
            $this->deleteSkus($stockCheck, $dataSkuIds, $user->id);
            $stockCheckSkus = $this->createOrUpdateStockCheckSkus($dataUpserts);
            if ($isRequestApprove) {
                $this->handleApprove($stockCheck, $stockCheckSkus, $user);
            } else {
                $stockCheck->update(['status' => StockCheck::STATUS_IN_PROGRESS]);
            }
            DB::commit();
            return $stockCheckSkus;
        } catch (\Exception $e) {
            Log::channel('factory')->info(__CLASS__ . '@' . __FUNCTION__, $data);
            Log::channel('factory')->error($e->getMessage(), $e->getTrace());
            DB::rollBack();
            throw $e;
        }
    }

    private function createOrUpdateStockCheckSkus($dataUpserts): Collection
    {
        $stockCheckSkus = collect();
        foreach ($dataUpserts as $dataSku) {
            if (isset($dataSku['id'])) {
                $stockCheckSkus->push($this->stockCheckSkuRepo->update($dataSku, $dataSku['id']));
            } else {
                $stockCheckSkus->push($this->stockCheckSkuRepo->create($dataSku));
            }
        }
        return $stockCheckSkus;
    }

    private function handleApprove(StockCheck $stockCheck, Collection $stockCheckSkus, $user)
    {
        $dataApprove = [
            'approve_staff_id' => $user->id,
            'approve_time' => Carbon::now(),
            'status' => StockCheck::STATUS_APPROVED,
        ];
        $stockCheck->update($dataApprove);
        $stockCheckSkus->each(function ($sku) use ($user) {
            $this->stockSkuService->update($sku->stock_sku_id, [
                'quantity' => $sku->quantity_real
            ], $user);
        });
    }

    private function deleteSkus(StockCheck $stockCheck, $skuIds, $userId)
    {
        $deleteSkus = $stockCheck->stockCheckSkus->reject(fn($sku) => $skuIds->contains($sku->id));
        $deleteSkus->each(function (StockCheckSku $sku) use ($userId) {
            $sku->delete();
        });
    }
}

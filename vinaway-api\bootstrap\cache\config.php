<?php return array (
  'app' => 
  array (
    'name' => 'Laravel',
    'env' => 'local',
    'debug' => true,
    'url' => 'http://localhost:8080',
    'asset_url' => NULL,
    'AWS_BUCKET' => '',
    'AWS_URL' => 'https://gtnstorage.s3.amazonaws.com',
    'OPENAI_API_KEY' => '********************************************************************************************************************************************************************',
    'allowed_internal_urls' => 
    array (
      0 => 'http://localhost:8080',
    ),
    'timezone' => 'America/New_York',
    'locale' => 'en',
    'fallback_locale' => 'en',
    'faker_locale' => 'en_US',
    'key' => 'base64:Myy/KM7duVEb9xNXbjl7DLkfZHwsM1Z0aSvmKp0mEzw=',
    'cipher' => 'AES-256-CBC',
    'providers' => 
    array (
      0 => 'Illuminate\\Auth\\AuthServiceProvider',
      1 => 'Illuminate\\Broadcasting\\BroadcastServiceProvider',
      2 => 'Illuminate\\Bus\\BusServiceProvider',
      3 => 'Illuminate\\Cache\\CacheServiceProvider',
      4 => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
      5 => 'Illuminate\\Cookie\\CookieServiceProvider',
      6 => 'Illuminate\\Database\\DatabaseServiceProvider',
      7 => 'Illuminate\\Encryption\\EncryptionServiceProvider',
      8 => 'Illuminate\\Filesystem\\FilesystemServiceProvider',
      9 => 'Illuminate\\Foundation\\Providers\\FoundationServiceProvider',
      10 => 'Illuminate\\Hashing\\HashServiceProvider',
      11 => 'Illuminate\\Mail\\MailServiceProvider',
      12 => 'Illuminate\\Notifications\\NotificationServiceProvider',
      13 => 'Illuminate\\Pagination\\PaginationServiceProvider',
      14 => 'Illuminate\\Pipeline\\PipelineServiceProvider',
      15 => 'Illuminate\\Queue\\QueueServiceProvider',
      16 => 'Illuminate\\Redis\\RedisServiceProvider',
      17 => 'Illuminate\\Auth\\Passwords\\PasswordResetServiceProvider',
      18 => 'Illuminate\\Session\\SessionServiceProvider',
      19 => 'Illuminate\\Translation\\TranslationServiceProvider',
      20 => 'Illuminate\\Validation\\ValidationServiceProvider',
      21 => 'Illuminate\\View\\ViewServiceProvider',
      22 => 'App\\Providers\\AppServiceProvider',
      23 => 'App\\Providers\\AuthServiceProvider',
      24 => 'App\\Providers\\EventServiceProvider',
      25 => 'App\\Providers\\RouteServiceProvider',
      26 => 'App\\Providers\\TelescopeServiceProvider',
      27 => 'App\\Providers\\DropboxServiceProvider',
      28 => 'Maatwebsite\\Excel\\ExcelServiceProvider',
    ),
    'aliases' => 
    array (
      'App' => 'Illuminate\\Support\\Facades\\App',
      'Arr' => 'Illuminate\\Support\\Arr',
      'Artisan' => 'Illuminate\\Support\\Facades\\Artisan',
      'Auth' => 'Illuminate\\Support\\Facades\\Auth',
      'Blade' => 'Illuminate\\Support\\Facades\\Blade',
      'Broadcast' => 'Illuminate\\Support\\Facades\\Broadcast',
      'Bus' => 'Illuminate\\Support\\Facades\\Bus',
      'Cache' => 'Illuminate\\Support\\Facades\\Cache',
      'Config' => 'Illuminate\\Support\\Facades\\Config',
      'Cookie' => 'Illuminate\\Support\\Facades\\Cookie',
      'Crypt' => 'Illuminate\\Support\\Facades\\Crypt',
      'Date' => 'Illuminate\\Support\\Facades\\Date',
      'DB' => 'Illuminate\\Support\\Facades\\DB',
      'Eloquent' => 'Illuminate\\Database\\Eloquent\\Model',
      'Event' => 'Illuminate\\Support\\Facades\\Event',
      'File' => 'Illuminate\\Support\\Facades\\File',
      'Gate' => 'Illuminate\\Support\\Facades\\Gate',
      'Hash' => 'Illuminate\\Support\\Facades\\Hash',
      'Http' => 'Illuminate\\Support\\Facades\\Http',
      'Js' => 'Illuminate\\Support\\Js',
      'Lang' => 'Illuminate\\Support\\Facades\\Lang',
      'Log' => 'Illuminate\\Support\\Facades\\Log',
      'Mail' => 'Illuminate\\Support\\Facades\\Mail',
      'Notification' => 'Illuminate\\Support\\Facades\\Notification',
      'Password' => 'Illuminate\\Support\\Facades\\Password',
      'Queue' => 'Illuminate\\Support\\Facades\\Queue',
      'RateLimiter' => 'Illuminate\\Support\\Facades\\RateLimiter',
      'Redirect' => 'Illuminate\\Support\\Facades\\Redirect',
      'Request' => 'Illuminate\\Support\\Facades\\Request',
      'Response' => 'Illuminate\\Support\\Facades\\Response',
      'Route' => 'Illuminate\\Support\\Facades\\Route',
      'Schema' => 'Illuminate\\Support\\Facades\\Schema',
      'Session' => 'Illuminate\\Support\\Facades\\Session',
      'Storage' => 'Illuminate\\Support\\Facades\\Storage',
      'Str' => 'Illuminate\\Support\\Str',
      'URL' => 'Illuminate\\Support\\Facades\\URL',
      'Validator' => 'Illuminate\\Support\\Facades\\Validator',
      'View' => 'Illuminate\\Support\\Facades\\View',
      'Excel' => 'Maatwebsite\\Excel\\Facades\\Excel',
    ),
  ),
  'auth' => 
  array (
    'defaults' => 
    array (
      'guard' => 'web',
      'passwords' => 'users',
    ),
    'guards' => 
    array (
      'web' => 
      array (
        'driver' => 'session',
        'provider' => 'users',
      ),
      'seller' => 
      array (
        'driver' => 'session',
        'provider' => 'sellers',
      ),
      'seller_api' => 
      array (
        'driver' => 'sanctum',
        'provider' => 'sellers',
      ),
      'designer' => 
      array (
        'driver' => 'session',
        'provider' => 'designers',
      ),
      'sanctum' => 
      array (
        'driver' => 'sanctum',
        'provider' => NULL,
      ),
    ),
    'providers' => 
    array (
      'users' => 
      array (
        'driver' => 'eloquent',
        'model' => 'App\\Models\\Staff',
      ),
      'sellers' => 
      array (
        'driver' => 'eloquent',
        'model' => 'App\\Models\\Seller',
      ),
      'designers' => 
      array (
        'driver' => 'eloquent',
        'model' => 'App\\Models\\DesignStaff',
      ),
    ),
    'passwords' => 
    array (
      'users' => 
      array (
        'provider' => 'users',
        'table' => 'password_resets',
        'expire' => 60,
        'throttle' => 60,
      ),
    ),
    'password_timeout' => 10800,
  ),
  'broadcasting' => 
  array (
    'default' => 'pusher',
    'connections' => 
    array (
      'pusher' => 
      array (
        'driver' => 'pusher',
        'key' => 'local',
        'secret' => 'local',
        'app_id' => 'local',
        'options' => 
        array (
          'cluster' => 'mt1',
          'useTLS' => true,
          'encrypted' => false,
          'host' => '127.0.0.1',
          'port' => '6001',
          'scheme' => 'http',
          'curl_options' => 
          array (
            81 => 0,
            64 => 0,
          ),
        ),
      ),
      'ably' => 
      array (
        'driver' => 'ably',
        'key' => NULL,
      ),
      'redis' => 
      array (
        'driver' => 'redis',
        'connection' => 'default',
      ),
      'log' => 
      array (
        'driver' => 'log',
      ),
      'null' => 
      array (
        'driver' => 'null',
      ),
    ),
  ),
  'cache' => 
  array (
    'default' => 'file',
    'stores' => 
    array (
      'apc' => 
      array (
        'driver' => 'apc',
      ),
      'array' => 
      array (
        'driver' => 'array',
        'serialize' => false,
      ),
      'database' => 
      array (
        'driver' => 'database',
        'table' => 'cache',
        'connection' => NULL,
        'lock_connection' => NULL,
      ),
      'file' => 
      array (
        'driver' => 'file',
        'path' => '/var/www/html/app/storage/framework/cache/data',
      ),
      'memcached' => 
      array (
        'driver' => 'memcached',
        'persistent_id' => NULL,
        'sasl' => 
        array (
          0 => NULL,
          1 => NULL,
        ),
        'options' => 
        array (
        ),
        'servers' => 
        array (
          0 => 
          array (
            'host' => '127.0.0.1',
            'port' => 11211,
            'weight' => 100,
          ),
        ),
      ),
      'redis' => 
      array (
        'driver' => 'redis',
        'connection' => 'cache',
        'lock_connection' => 'default',
      ),
      'dynamodb' => 
      array (
        'driver' => 'dynamodb',
        'key' => '',
        'secret' => '',
        'region' => 'us-east-1',
        'table' => 'cache',
        'endpoint' => NULL,
      ),
      'octane' => 
      array (
        'driver' => 'octane',
      ),
    ),
    'prefix' => 'laravel_cache',
  ),
  'cors' => 
  array (
    'paths' => 
    array (
      0 => 'api/*',
      1 => 'sanctum/csrf-cookie',
    ),
    'allowed_methods' => 
    array (
      0 => '*',
      1 => 'https://dev.seller.vinaway.io',
      2 => 'https://dev.designer.vinaway.io',
      3 => 'https://dev.factory.vinaway.io',
      4 => 'https://dev.api.vinaway.io',
      5 => 'https://api.vinaway.io',
      6 => 'https://seller.vinaway.io',
      7 => 'https://designer.vinaway.io',
      8 => 'https://factory.vinaway.io',
    ),
    'allowed_origins' => 
    array (
      0 => '*',
    ),
    'allowed_origins_patterns' => 
    array (
    ),
    'allowed_headers' => 
    array (
      0 => '*',
    ),
    'exposed_headers' => 
    array (
    ),
    'max_age' => 1728000,
    'supports_credentials' => false,
  ),
  'database' => 
  array (
    'default' => 'mysql',
    'connections' => 
    array (
      'sqlite' => 
      array (
        'driver' => 'sqlite',
        'url' => NULL,
        'database' => 'local_vinaway_prod',
        'prefix' => '',
        'foreign_key_constraints' => true,
      ),
      'mysql' => 
      array (
        'driver' => 'mysql',
        'url' => NULL,
        'host' => 'db',
        'port' => '3306',
        'database' => 'local_vinaway_prod',
        'username' => 'root',
        'password' => '12345678',
        'unix_socket' => '',
        'charset' => 'utf8',
        'collation' => 'utf8_general_ci',
        'prefix' => '',
        'prefix_indexes' => true,
        'strict' => false,
        'engine' => NULL,
        'options' => 
        array (
        ),
      ),
      'ecom' => 
      array (
        'driver' => 'mysql',
        'url' => NULL,
        'host' => '127.0.0.1',
        'port' => '3306',
        'database' => 'forge',
        'username' => 'forge',
        'password' => '',
        'unix_socket' => '',
        'charset' => 'utf8mb4',
        'collation' => 'utf8mb4_unicode_ci',
        'prefix' => '',
        'prefix_indexes' => true,
        'strict' => false,
        'engine' => NULL,
        'options' => 
        array (
        ),
      ),
      'pgsql' => 
      array (
        'driver' => 'pgsql',
        'url' => NULL,
        'host' => 'db',
        'port' => '3306',
        'database' => 'local_vinaway_prod',
        'username' => 'root',
        'password' => '12345678',
        'charset' => 'utf8',
        'prefix' => '',
        'prefix_indexes' => true,
        'schema' => 'public',
        'sslmode' => 'prefer',
      ),
      'sqlsrv' => 
      array (
        'driver' => 'sqlsrv',
        'url' => NULL,
        'host' => 'db',
        'port' => '3306',
        'database' => 'local_vinaway_prod',
        'username' => 'root',
        'password' => '12345678',
        'charset' => 'utf8',
        'prefix' => '',
        'prefix_indexes' => true,
      ),
      'redis' => 
      array (
        'client' => 'phpredis',
        'options' => 
        array (
          'cluster' => 'redis',
          'prefix' => 'laravel_database_',
        ),
        'default' => 
        array (
          'url' => NULL,
          'host' => '127.0.0.1',
          'username' => NULL,
          'password' => NULL,
          'port' => '6379',
          'database' => '0',
        ),
        'cache' => 
        array (
          'url' => NULL,
          'host' => '127.0.0.1',
          'username' => NULL,
          'password' => NULL,
          'port' => '6379',
          'database' => '1',
        ),
      ),
    ),
    'migrations' => 'migrations',
    'redis' => 
    array (
      'client' => 'phpredis',
      'options' => 
      array (
        'cluster' => 'redis',
        'prefix' => 'laravel_database_',
      ),
      'default' => 
      array (
        'url' => NULL,
        'host' => '127.0.0.1',
        'password' => NULL,
        'port' => '6379',
        'database' => '0',
      ),
      'cache' => 
      array (
        'url' => NULL,
        'host' => '127.0.0.1',
        'password' => NULL,
        'port' => '6379',
        'database' => '1',
      ),
    ),
  ),
  'design' => 
  array (
    'board_statuses' => 
    array (
      'pending' => 
      array (
        'id' => 1,
        'title' => 'Pending',
      ),
      'todo' => 
      array (
        'id' => 2,
        'title' => 'Todo',
      ),
      'doing' => 
      array (
        'id' => 3,
        'title' => 'Doing',
      ),
      'leader_check' => 
      array (
        'id' => 4,
        'title' => 'Leader Check',
      ),
      'seller_confirm' => 
      array (
        'id' => 5,
        'title' => 'Seller Confirm',
      ),
      'need_fix' => 
      array (
        'id' => 6,
        'title' => 'Need Fix',
      ),
      'done' => 
      array (
        'id' => 7,
        'title' => 'Done',
      ),
      'canceled' => 
      array (
        'id' => 8,
        'title' => 'Canceled',
      ),
    ),
  ),
  'design_files' => 
  array (
    'for_customer' => 
    array (
      0 => 'DST',
      1 => 'PES',
      2 => 'EXP',
      3 => 'JEF',
      4 => 'VP3',
      5 => 'HUS',
      6 => 'ART',
      7 => 'XXX',
      8 => 'PNG',
      9 => 'PDF',
    ),
  ),
  'excel' => 
  array (
    'exports' => 
    array (
      'chunk_size' => 1000,
      'pre_calculate_formulas' => false,
      'strict_null_comparison' => false,
      'csv' => 
      array (
        'delimiter' => ',',
        'enclosure' => '"',
        'line_ending' => '
',
        'use_bom' => false,
        'include_separator_line' => false,
        'excel_compatibility' => false,
        'output_encoding' => '',
        'test_auto_detect' => true,
      ),
      'properties' => 
      array (
        'creator' => '',
        'lastModifiedBy' => '',
        'title' => '',
        'description' => '',
        'subject' => '',
        'keywords' => '',
        'category' => '',
        'manager' => '',
        'company' => '',
      ),
    ),
    'imports' => 
    array (
      'read_only' => true,
      'ignore_empty' => false,
      'heading_row' => 
      array (
        'formatter' => 'slug',
      ),
      'csv' => 
      array (
        'delimiter' => NULL,
        'enclosure' => '"',
        'escape_character' => '\\',
        'contiguous' => false,
        'input_encoding' => 'guess',
      ),
      'properties' => 
      array (
        'creator' => '',
        'lastModifiedBy' => '',
        'title' => '',
        'description' => '',
        'subject' => '',
        'keywords' => '',
        'category' => '',
        'manager' => '',
        'company' => '',
      ),
      'cells' => 
      array (
        'middleware' => 
        array (
        ),
      ),
    ),
    'extension_detector' => 
    array (
      'xlsx' => 'Xlsx',
      'xlsm' => 'Xlsx',
      'xltx' => 'Xlsx',
      'xltm' => 'Xlsx',
      'xls' => 'Xls',
      'xlt' => 'Xls',
      'ods' => 'Ods',
      'ots' => 'Ods',
      'slk' => 'Slk',
      'xml' => 'Xml',
      'gnumeric' => 'Gnumeric',
      'htm' => 'Html',
      'html' => 'Html',
      'csv' => 'Csv',
      'tsv' => 'Csv',
      'pdf' => 'Dompdf',
    ),
    'value_binder' => 
    array (
      'default' => 'Maatwebsite\\Excel\\DefaultValueBinder',
    ),
    'cache' => 
    array (
      'driver' => 'memory',
      'batch' => 
      array (
        'memory_limit' => 60000,
      ),
      'illuminate' => 
      array (
        'store' => NULL,
      ),
      'default_ttl' => 10800,
    ),
    'transactions' => 
    array (
      'handler' => 'db',
      'db' => 
      array (
        'connection' => NULL,
      ),
    ),
    'temporary_files' => 
    array (
      'local_path' => '/var/www/html/app/storage/framework/cache/laravel-excel',
      'local_permissions' => 
      array (
      ),
      'remote_disk' => NULL,
      'remote_prefix' => NULL,
      'force_resync_remote' => NULL,
    ),
  ),
  'filesystems' => 
  array (
    'default' => 'public',
    'disks' => 
    array (
      'local' => 
      array (
        'driver' => 'local',
        'root' => '/var/www/html/app/storage/app',
      ),
      'public' => 
      array (
        'driver' => 'local',
        'root' => '/var/www/html/app/storage/app/public',
        'url' => 'http://localhost:8080/storage',
        'visibility' => 'public',
      ),
      's3' => 
      array (
        'driver' => 's3',
        'key' => '',
        'secret' => '',
        'region' => 'us-east-1',
        'bucket' => '',
        'url' => NULL,
        'endpoint' => NULL,
        'use_path_style_endpoint' => false,
      ),
      'gcs' => 
      array (
        'driver' => 'gcs',
        'project_id' => 'vinaway-dev',
        'key_file_path' => '/var/www/vinaway-api/storage/gcs_key.json',
        'key_file' => NULL,
        'bucket' => 'cdn.vinaway.io',
        'path_prefix' => 'vi',
        'storage_api_uri' => 'https://cdn.vinaway.io',
        'api_endpoint' => NULL,
        'visibility' => 'public',
        'visibility_handler' => NULL,
        'metadata' => 
        array (
          'cacheControl' => 'public,max-age=86400',
        ),
      ),
      'dropbox' => 
      array (
        'driver' => 'dropbox',
        'authorization_token' => NULL,
        'app_key' => NULL,
        'app_secret' => NULL,
        'refresh_token' => NULL,
        'folder' => '',
        'url' => '',
      ),
      'tmp' => 
      array (
        'driver' => 'local',
        'root' => '/var/www/html/app/storage/tmp',
        'visibility' => 'public',
      ),
    ),
    'links' => 
    array (
      '/var/www/html/app/public/storage' => '/var/www/html/app/storage/app/public',
    ),
  ),
  'flashship' => 
  array (
    'options' => 
    array (
      'T-Shirt' => 
      array (
        'S' => 
        array (
          0 => 
          array (
            0 => 'ORANGE',
            1 => '#f75f36',
            2 => 12012,
          ),
          1 => 
          array (
            0 => 'BLACK',
            1 => '#000000',
            2 => 12126,
          ),
          2 => 
          array (
            0 => 'WHITE',
            1 => '#ffffff',
            2 => 12102,
          ),
          3 => 
          array (
            0 => 'LIGHT BLUE',
            1 => '#a3b3cb',
            2 => 11958,
          ),
          4 => 
          array (
            0 => 'CHARCOAL',
            1 => '#66676c',
            2 => 11874,
          ),
          5 => 
          array (
            0 => 'NAVY',
            1 => '#263147',
            2 => 11988,
          ),
          6 => 
          array (
            0 => 'RED',
            1 => '#d50032',
            2 => 12024,
          ),
          7 => 
          array (
            0 => 'SPORT GREY',
            1 => '#97999b',
            2 => 12072,
          ),
          8 => 
          array (
            0 => 'MAROON',
            1 => '#5b2b42',
            2 => 11976,
          ),
          9 => 
          array (
            0 => 'DARK HEATHER',
            1 => '#425563',
            2 => 11904,
          ),
          10 => 
          array (
            0 => 'ROYAL BLUE',
            1 => '#224d8f',
            2 => 91943,
          ),
          11 => 
          array (
            0 => 'LIGHT PINK',
            1 => '#e4c6d4',
            2 => 11964,
          ),
          12 => 
          array (
            0 => 'SAND',
            1 => '#cabfad',
            2 => 12054,
          ),
          13 => 
          array (
            0 => 'FOREST GREEN',
            1 => '#228b22',
            2 => 12144,
          ),
          14 => 
          array (
            0 => 'ASH GREY',
            1 => '#42716',
            2 => 42716,
          ),
          15 => 
          array (
            0 => 'PURPLE',
            1 => '#42716',
            2 => 12018,
          ),
          16 => 
          array (
            0 => 'MILITARY GREEN',
            1 => '#228b22',
            2 => 12192,
          ),
        ),
        'M' => 
        array (
          0 => 
          array (
            0 => 'ORANGE',
            1 => '#f75f36',
            2 => 12011,
          ),
          1 => 
          array (
            0 => 'BLACK',
            1 => '#000000',
            2 => 12125,
          ),
          2 => 
          array (
            0 => 'WHITE',
            1 => '#ffffff',
            2 => 12101,
          ),
          3 => 
          array (
            0 => 'LIGHT BLUE',
            1 => '#a3b3cb',
            2 => 11957,
          ),
          4 => 
          array (
            0 => 'CHARCOAL',
            1 => '#66676c',
            2 => 11873,
          ),
          5 => 
          array (
            0 => 'NAVY',
            1 => '#263147',
            2 => 11987,
          ),
          6 => 
          array (
            0 => 'RED',
            1 => '#d50032',
            2 => 12023,
          ),
          7 => 
          array (
            0 => 'SPORT GREY',
            1 => '#97999b',
            2 => 12071,
          ),
          8 => 
          array (
            0 => 'MAROON',
            1 => '#5b2b42',
            2 => 11975,
          ),
          9 => 
          array (
            0 => 'DARK HEATHER',
            1 => '#425563',
            2 => 11903,
          ),
          10 => 
          array (
            0 => 'ROYAL BLUE',
            1 => '#224d8f',
            2 => 91944,
          ),
          11 => 
          array (
            0 => 'LIGHT PINK',
            1 => '#e4c6d4',
            2 => 11963,
          ),
          12 => 
          array (
            0 => 'SAND',
            1 => '#cabfad',
            2 => 12053,
          ),
          13 => 
          array (
            0 => 'FOREST GREEN',
            1 => '#228b22',
            2 => 12143,
          ),
          14 => 
          array (
            0 => 'ASH GREY',
            1 => '#42716',
            2 => 42717,
          ),
          15 => 
          array (
            0 => 'PURPLE',
            1 => '#42716',
            2 => 12017,
          ),
          16 => 
          array (
            0 => 'MILITARY GREEN',
            1 => '#228b22',
            2 => 12191,
          ),
        ),
        'L' => 
        array (
          0 => 
          array (
            0 => 'ORANGE',
            1 => '#f75f36',
            2 => 12010,
          ),
          1 => 
          array (
            0 => 'BLACK',
            1 => '#000000',
            2 => 12124,
          ),
          2 => 
          array (
            0 => 'WHITE',
            1 => '#ffffff',
            2 => 12100,
          ),
          3 => 
          array (
            0 => 'LIGHT BLUE',
            1 => '#a3b3cb',
            2 => 11956,
          ),
          4 => 
          array (
            0 => 'CHARCOAL',
            1 => '#66676c',
            2 => 11872,
          ),
          5 => 
          array (
            0 => 'NAVY',
            1 => '#263147',
            2 => 11986,
          ),
          6 => 
          array (
            0 => 'RED',
            1 => '#d50032',
            2 => 12022,
          ),
          7 => 
          array (
            0 => 'SPORT GREY',
            1 => '#97999b',
            2 => 12070,
          ),
          8 => 
          array (
            0 => 'MAROON',
            1 => '#5b2b42',
            2 => 11974,
          ),
          9 => 
          array (
            0 => 'DARK HEATHER',
            1 => '#425563',
            2 => 11902,
          ),
          10 => 
          array (
            0 => 'ROYAL BLUE',
            1 => '#224d8f',
            2 => 91945,
          ),
          11 => 
          array (
            0 => 'LIGHT PINK',
            1 => '#e4c6d4',
            2 => 11962,
          ),
          12 => 
          array (
            0 => 'SAND',
            1 => '#cabfad',
            2 => 12052,
          ),
          13 => 
          array (
            0 => 'FOREST GREEN',
            1 => '#228b22',
            2 => 12142,
          ),
          14 => 
          array (
            0 => 'ASH GREY',
            1 => '#42716',
            2 => 42718,
          ),
          15 => 
          array (
            0 => 'PURPLE',
            1 => '#42716',
            2 => 12016,
          ),
          16 => 
          array (
            0 => 'MILITARY GREEN',
            1 => '#228b22',
            2 => 12190,
          ),
        ),
        'XL' => 
        array (
          0 => 
          array (
            0 => 'ORANGE',
            1 => '#f75f36',
            2 => 12013,
          ),
          1 => 
          array (
            0 => 'BLACK',
            1 => '#000000',
            2 => 12127,
          ),
          2 => 
          array (
            0 => 'WHITE',
            1 => '#ffffff',
            2 => 12103,
          ),
          3 => 
          array (
            0 => 'LIGHT BLUE',
            1 => '#a3b3cb',
            2 => 11959,
          ),
          4 => 
          array (
            0 => 'CHARCOAL',
            1 => '#66676c',
            2 => 11875,
          ),
          5 => 
          array (
            0 => 'NAVY',
            1 => '#263147',
            2 => 11989,
          ),
          6 => 
          array (
            0 => 'RED',
            1 => '#d50032',
            2 => 12025,
          ),
          7 => 
          array (
            0 => 'SPORT GREY',
            1 => '#97999b',
            2 => 12073,
          ),
          8 => 
          array (
            0 => 'MAROON',
            1 => '#5b2b42',
            2 => 11977,
          ),
          9 => 
          array (
            0 => 'DARK HEATHER',
            1 => '#425563',
            2 => 11905,
          ),
          10 => 
          array (
            0 => 'ROYAL BLUE',
            1 => '#224d8f',
            2 => 91946,
          ),
          11 => 
          array (
            0 => 'LIGHT PINK',
            1 => '#e4c6d4',
            2 => 11965,
          ),
          12 => 
          array (
            0 => 'SAND',
            1 => '#cabfad',
            2 => 12055,
          ),
          13 => 
          array (
            0 => 'FOREST GREEN',
            1 => '#228b22',
            2 => 12145,
          ),
          14 => 
          array (
            0 => 'ASH GREY',
            1 => '#42716',
            2 => 42719,
          ),
          15 => 
          array (
            0 => 'PURPLE',
            1 => '#42716',
            2 => 12019,
          ),
          16 => 
          array (
            0 => 'MILITARY GREEN',
            1 => '#228b22',
            2 => 12193,
          ),
        ),
        '2XL' => 
        array (
          0 => 
          array (
            0 => 'ORANGE',
            1 => '#f75f36',
            2 => 12014,
          ),
          1 => 
          array (
            0 => 'BLACK',
            1 => '#000000',
            2 => 12128,
          ),
          2 => 
          array (
            0 => 'WHITE',
            1 => '#ffffff',
            2 => 12104,
          ),
          3 => 
          array (
            0 => 'LIGHT BLUE',
            1 => '#a3b3cb',
            2 => 11960,
          ),
          4 => 
          array (
            0 => 'CHARCOAL',
            1 => '#66676c',
            2 => 11876,
          ),
          5 => 
          array (
            0 => 'NAVY',
            1 => '#263147',
            2 => 11990,
          ),
          6 => 
          array (
            0 => 'RED',
            1 => '#d50032',
            2 => 12026,
          ),
          7 => 
          array (
            0 => 'SPORT GREY',
            1 => '#97999b',
            2 => 12074,
          ),
          8 => 
          array (
            0 => 'MAROON',
            1 => '#5b2b42',
            2 => 11978,
          ),
          9 => 
          array (
            0 => 'DARK HEATHER',
            1 => '#425563',
            2 => 11906,
          ),
          10 => 
          array (
            0 => 'ROYAL BLUE',
            1 => '#224d8f',
            2 => 91947,
          ),
          11 => 
          array (
            0 => 'LIGHT PINK',
            1 => '#e4c6d4',
            2 => 11966,
          ),
          12 => 
          array (
            0 => 'SAND',
            1 => '#cabfad',
            2 => 12056,
          ),
          13 => 
          array (
            0 => 'FOREST GREEN',
            1 => '#228b22',
            2 => 12146,
          ),
          14 => 
          array (
            0 => 'ASH GREY',
            1 => '#42716',
            2 => 42720,
          ),
          15 => 
          array (
            0 => 'PURPLE',
            1 => '#42716',
            2 => 12020,
          ),
          16 => 
          array (
            0 => 'MILITARY GREEN',
            1 => '#228b22',
            2 => 12194,
          ),
        ),
        '3XL' => 
        array (
          0 => 
          array (
            0 => 'ORANGE',
            1 => '#f75f36',
            2 => 12015,
          ),
          1 => 
          array (
            0 => 'BLACK',
            1 => '#000000',
            2 => 12129,
          ),
          2 => 
          array (
            0 => 'WHITE',
            1 => '#ffffff',
            2 => 12105,
          ),
          3 => 
          array (
            0 => 'LIGHT BLUE',
            1 => '#a3b3cb',
            2 => 11961,
          ),
          4 => 
          array (
            0 => 'CHARCOAL',
            1 => '#66676c',
            2 => 11877,
          ),
          5 => 
          array (
            0 => 'NAVY',
            1 => '#263147',
            2 => 11991,
          ),
          6 => 
          array (
            0 => 'RED',
            1 => '#d50032',
            2 => 12027,
          ),
          7 => 
          array (
            0 => 'SPORT GREY',
            1 => '#97999b',
            2 => 12075,
          ),
          8 => 
          array (
            0 => 'MAROON',
            1 => '#5b2b42',
            2 => 11979,
          ),
          9 => 
          array (
            0 => 'DARK HEATHER',
            1 => '#425563',
            2 => 11907,
          ),
          10 => 
          array (
            0 => 'ROYAL BLUE',
            1 => '#224d8f',
            2 => 91948,
          ),
          11 => 
          array (
            0 => 'LIGHT PINK',
            1 => '#e4c6d4',
            2 => 11967,
          ),
          12 => 
          array (
            0 => 'SAND',
            1 => '#cabfad',
            2 => 12057,
          ),
          13 => 
          array (
            0 => 'FOREST GREEN',
            1 => '#228b22',
            2 => 12147,
          ),
          14 => 
          array (
            0 => 'ASH GREY',
            1 => '#42716',
            2 => 42721,
          ),
          15 => 
          array (
            0 => 'PURPLE',
            1 => '#42716',
            2 => 12021,
          ),
          16 => 
          array (
            0 => 'MILITARY GREEN',
            1 => '#228b22',
            2 => 12195,
          ),
        ),
        '4XL' => 
        array (
          0 => 
          array (
            0 => 'ORANGE',
            1 => '#f75f36',
            2 => 24001,
          ),
          1 => 
          array (
            0 => 'BLACK',
            1 => '#000000',
            2 => 24039,
          ),
          2 => 
          array (
            0 => 'WHITE',
            1 => '#ffffff',
            2 => 24031,
          ),
          3 => 
          array (
            0 => 'LIGHT BLUE',
            1 => '#a3b3cb',
            2 => 23983,
          ),
          4 => 
          array (
            0 => 'CHARCOAL',
            1 => '#66676c',
            2 => 23955,
          ),
          5 => 
          array (
            0 => 'NAVY',
            1 => '#263147',
            2 => 23993,
          ),
          6 => 
          array (
            0 => 'RED',
            1 => '#d50032',
            2 => 24005,
          ),
          7 => 
          array (
            0 => 'SPORT GREY',
            1 => '#97999b',
            2 => 24021,
          ),
          8 => 
          array (
            0 => 'MAROON',
            1 => '#5b2b42',
            2 => 23989,
          ),
          9 => 
          array (
            0 => 'DARK HEATHER',
            1 => '#425563',
            2 => 23965,
          ),
          10 => 
          array (
            0 => 'ROYAL BLUE',
            1 => '#224d8f',
            2 => 64268,
          ),
          11 => 
          array (
            0 => 'LIGHT PINK',
            1 => '#e4c6d4',
            2 => 23985,
          ),
          12 => 
          array (
            0 => 'SAND',
            1 => '#cabfad',
            2 => 24015,
          ),
          13 => 
          array (
            0 => 'FOREST GREEN',
            1 => '#228b22',
            2 => 24045,
          ),
          14 => 
          array (
            0 => 'ASH GREY',
            1 => '#42716',
            2 => 23947,
          ),
          15 => 
          array (
            0 => 'PURPLE',
            1 => '#42716',
            2 => 24003,
          ),
          16 => 
          array (
            0 => 'MILITARY GREEN',
            1 => '#228b22',
            2 => 24060,
          ),
        ),
        '5XL' => 
        array (
          0 => 
          array (
            0 => 'ORANGE',
            1 => '#f75f36',
            2 => 24134,
          ),
          1 => 
          array (
            0 => 'BLACK',
            1 => '#000000',
            2 => 24171,
          ),
          2 => 
          array (
            0 => 'WHITE',
            1 => '#ffffff',
            2 => 24164,
          ),
          3 => 
          array (
            0 => 'LIGHT BLUE',
            1 => '#a3b3cb',
            2 => 21894,
          ),
          4 => 
          array (
            0 => 'CHARCOAL',
            1 => '#66676c',
            2 => 24088,
          ),
          5 => 
          array (
            0 => 'NAVY',
            1 => '#263147',
            2 => 24126,
          ),
          6 => 
          array (
            0 => 'RED',
            1 => '#d50032',
            2 => 24138,
          ),
          7 => 
          array (
            0 => 'SPORT GREY',
            1 => '#97999b',
            2 => 24153,
          ),
          8 => 
          array (
            0 => 'MAROON',
            1 => '#5b2b42',
            2 => 24122,
          ),
          9 => 
          array (
            0 => 'DARK HEATHER',
            1 => '#425563',
            2 => 24099,
          ),
          10 => 
          array (
            0 => 'ROYAL BLUE',
            1 => '#224d8f',
            2 => 64281,
          ),
          11 => 
          array (
            0 => 'LIGHT PINK',
            1 => '#e4c6d4',
            2 => 24118,
          ),
          12 => 
          array (
            0 => 'SAND',
            1 => '#cabfad',
            2 => 24147,
          ),
          13 => 
          array (
            0 => 'FOREST GREEN',
            1 => '#228b22',
            2 => 24178,
          ),
          14 => 
          array (
            0 => 'ASH GREY',
            1 => '#42716',
            2 => 24081,
          ),
          15 => 
          array (
            0 => 'PURPLE',
            1 => '#42716',
            2 => 24136,
          ),
          16 => 
          array (
            0 => 'MILITARY GREEN',
            1 => '#228b22',
            2 => 24194,
          ),
        ),
      ),
      'Hoodie' => 
      array (
        'S' => 
        array (
          0 => 
          array (
            0 => 'BLACK',
            1 => '#000000',
            2 => 72997,
          ),
          1 => 
          array (
            0 => 'WHITE',
            1 => '#ffffff',
            2 => 73009,
          ),
          2 => 
          array (
            0 => 'NAVY',
            1 => '#263147',
            2 => 32894,
          ),
          3 => 
          array (
            0 => 'SPORT GREY',
            1 => '#97999b',
            2 => 32902,
          ),
          4 => 
          array (
            0 => 'SAND',
            1 => '#cabfad',
            2 => 42164,
          ),
          5 => 
          array (
            0 => 'FOREST GREEN',
            1 => '#cabfad',
            2 => 79457,
          ),
          6 => 
          array (
            0 => 'LIGHT PINK',
            1 => '#e4c6d4',
            2 => 73004,
          ),
        ),
        'M' => 
        array (
          0 => 
          array (
            0 => 'BLACK',
            1 => '#000000',
            2 => 73011,
          ),
          1 => 
          array (
            0 => 'WHITE',
            1 => '#ffffff',
            2 => 73023,
          ),
          2 => 
          array (
            0 => 'NAVY',
            1 => '#263147',
            2 => 32895,
          ),
          3 => 
          array (
            0 => 'SPORT GREY',
            1 => '#97999b',
            2 => 32903,
          ),
          4 => 
          array (
            0 => 'SAND',
            1 => '#cabfad',
            2 => 42165,
          ),
          5 => 
          array (
            0 => 'FOREST GREEN',
            1 => '#cabfad',
            2 => 79458,
          ),
          6 => 
          array (
            0 => 'LIGHT PINK',
            1 => '#e4c6d4',
            2 => 73018,
          ),
        ),
        'L' => 
        array (
          0 => 
          array (
            0 => 'BLACK',
            1 => '#000000',
            2 => 73025,
          ),
          1 => 
          array (
            0 => 'WHITE',
            1 => '#ffffff',
            2 => 73037,
          ),
          2 => 
          array (
            0 => 'NAVY',
            1 => '#263147',
            2 => 32896,
          ),
          3 => 
          array (
            0 => 'SPORT GREY',
            1 => '#97999b',
            2 => 32904,
          ),
          4 => 
          array (
            0 => 'SAND',
            1 => '#cabfad',
            2 => 42166,
          ),
          5 => 
          array (
            0 => 'FOREST GREEN',
            1 => '#cabfad',
            2 => 79459,
          ),
          6 => 
          array (
            0 => 'LIGHT PINK',
            1 => '#e4c6d4',
            2 => 73032,
          ),
        ),
        'XL' => 
        array (
          0 => 
          array (
            0 => 'BLACK',
            1 => '#000000',
            2 => 73039,
          ),
          1 => 
          array (
            0 => 'WHITE',
            1 => '#ffffff',
            2 => 73051,
          ),
          2 => 
          array (
            0 => 'NAVY',
            1 => '#263147',
            2 => 32897,
          ),
          3 => 
          array (
            0 => 'SPORT GREY',
            1 => '#97999b',
            2 => 32905,
          ),
          4 => 
          array (
            0 => 'SAND',
            1 => '#cabfad',
            2 => 42167,
          ),
          5 => 
          array (
            0 => 'FOREST GREEN',
            1 => '#cabfad',
            2 => 79460,
          ),
          6 => 
          array (
            0 => 'LIGHT PINK',
            1 => '#e4c6d4',
            2 => 73046,
          ),
        ),
        '2XL' => 
        array (
          0 => 
          array (
            0 => 'BLACK',
            1 => '#000000',
            2 => 73053,
          ),
          1 => 
          array (
            0 => 'WHITE',
            1 => '#ffffff',
            2 => 73065,
          ),
          2 => 
          array (
            0 => 'NAVY',
            1 => '#263147',
            2 => 32898,
          ),
          3 => 
          array (
            0 => 'SPORT GREY',
            1 => '#97999b',
            2 => 32906,
          ),
          4 => 
          array (
            0 => 'SAND',
            1 => '#cabfad',
            2 => 42168,
          ),
          5 => 
          array (
            0 => 'FOREST GREEN',
            1 => '#cabfad',
            2 => 79461,
          ),
          6 => 
          array (
            0 => 'LIGHT PINK',
            1 => '#e4c6d4',
            2 => 73060,
          ),
        ),
        '3XL' => 
        array (
          0 => 
          array (
            0 => 'BLACK',
            1 => '#000000',
            2 => 79471,
          ),
          1 => 
          array (
            0 => 'WHITE',
            1 => '#ffffff',
            2 => 73079,
          ),
          2 => 
          array (
            0 => 'NAVY',
            1 => '#263147',
            2 => 32899,
          ),
          3 => 
          array (
            0 => 'SPORT GREY',
            1 => '#97999b',
            2 => 32907,
          ),
          4 => 
          array (
            0 => 'SAND',
            1 => '#cabfad',
            2 => 42169,
          ),
          5 => 
          array (
            0 => 'FOREST GREEN',
            1 => '#cabfad',
            2 => 79462,
          ),
          6 => 
          array (
            0 => 'LIGHT PINK',
            1 => '#e4c6d4',
            2 => 73074,
          ),
        ),
        '4XL' => 
        array (
          0 => 
          array (
            0 => 'BLACK',
            1 => '#000000',
            2 => 73081,
          ),
          1 => 
          array (
            0 => 'WHITE',
            1 => '#ffffff',
            2 => 73093,
          ),
          2 => 
          array (
            0 => 'NAVY',
            1 => '#263147',
            2 => 32900,
          ),
          3 => 
          array (
            0 => 'SPORT GREY',
            1 => '#97999b',
            2 => 32908,
          ),
          4 => 
          array (
            0 => 'SAND',
            1 => '#cabfad',
            2 => 42170,
          ),
          5 => 
          array (
            0 => 'FOREST GREEN',
            1 => '#cabfad',
            2 => 33423,
          ),
          6 => 
          array (
            0 => 'LIGHT PINK',
            1 => '#e4c6d4',
            2 => 73088,
          ),
        ),
        '5XL' => 
        array (
          0 => 
          array (
            0 => 'BLACK',
            1 => '#000000',
            2 => 73095,
          ),
          1 => 
          array (
            0 => 'WHITE',
            1 => '#ffffff',
            2 => 73107,
          ),
          2 => 
          array (
            0 => 'NAVY',
            1 => '#263147',
            2 => 32901,
          ),
          3 => 
          array (
            0 => 'SPORT GREY',
            1 => '#97999b',
            2 => 32909,
          ),
          4 => 
          array (
            0 => 'SAND',
            1 => '#cabfad',
            2 => 42171,
          ),
          5 => 
          array (
            0 => 'FOREST GREEN',
            1 => '#cabfad',
            2 => 33424,
          ),
          6 => 
          array (
            0 => 'LIGHT PINK',
            1 => '#e4c6d4',
            2 => 73102,
          ),
        ),
      ),
      'Sweatshirt' => 
      array (
        'S' => 
        array (
          0 => 
          array (
            0 => 'ORANGE',
            1 => '#f75f36',
            2 => 25389,
          ),
          1 => 
          array (
            0 => 'BLACK',
            1 => '#000000',
            2 => 25397,
          ),
          2 => 
          array (
            0 => 'WHITE',
            1 => '#ffffff',
            2 => 25396,
          ),
          3 => 
          array (
            0 => 'FOREST GREEN',
            1 => '#228B22',
            2 => 25400,
          ),
          4 => 
          array (
            0 => 'SAND',
            1 => '#cabfad',
            2 => 25394,
          ),
          5 => 
          array (
            0 => 'SPORT GREY',
            1 => '#97999b',
            2 => 25395,
          ),
          6 => 
          array (
            0 => 'LIGHT PINK',
            1 => '#e4c6d4',
            2 => 25386,
          ),
          7 => 
          array (
            0 => 'MILITARY GREEN',
            1 => '#228b22',
            2 => 25404,
          ),
          8 => 
          array (
            0 => 'MAROON',
            1 => '#5b2b42',
            2 => 25387,
          ),
          9 => 
          array (
            0 => 'RED',
            1 => '#5b2b42',
            2 => 25391,
          ),
          10 => 
          array (
            0 => 'ROYAL BLUE',
            1 => '#5b2b42',
            2 => 25625,
          ),
          11 => 
          array (
            0 => 'ASH GREY',
            1 => '#5b2b42',
            2 => 79256,
          ),
        ),
        'M' => 
        array (
          0 => 
          array (
            0 => 'ORANGE',
            1 => '#f75f36',
            2 => 25420,
          ),
          1 => 
          array (
            0 => 'BLACK',
            1 => '#000000',
            2 => 25428,
          ),
          2 => 
          array (
            0 => 'WHITE',
            1 => '#ffffff',
            2 => 25427,
          ),
          3 => 
          array (
            0 => 'FOREST GREEN',
            1 => '#228B22',
            2 => 25431,
          ),
          4 => 
          array (
            0 => 'SAND',
            1 => '#cabfad',
            2 => 25425,
          ),
          5 => 
          array (
            0 => 'SPORT GREY',
            1 => '#97999b',
            2 => 25426,
          ),
          6 => 
          array (
            0 => 'LIGHT PINK',
            1 => '#e4c6d4',
            2 => 25417,
          ),
          7 => 
          array (
            0 => 'MILITARY GREEN',
            1 => '#228b22',
            2 => 25435,
          ),
          8 => 
          array (
            0 => 'MAROON',
            1 => '#5b2b42',
            2 => 25418,
          ),
          9 => 
          array (
            0 => 'RED',
            1 => '#5b2b42',
            2 => 25422,
          ),
          10 => 
          array (
            0 => 'ROYAL BLUE',
            1 => '#5b2b42',
            2 => 25624,
          ),
          11 => 
          array (
            0 => 'ASH GREY',
            1 => '#5b2b42',
            2 => 79257,
          ),
        ),
        'L' => 
        array (
          0 => 
          array (
            0 => 'ORANGE',
            1 => '#f75f36',
            2 => 25451,
          ),
          1 => 
          array (
            0 => 'BLACK',
            1 => '#000000',
            2 => 25459,
          ),
          2 => 
          array (
            0 => 'WHITE',
            1 => '#ffffff',
            2 => 25458,
          ),
          3 => 
          array (
            0 => 'FOREST GREEN',
            1 => '#228B22',
            2 => 25462,
          ),
          4 => 
          array (
            0 => 'SAND',
            1 => '#cabfad',
            2 => 25456,
          ),
          5 => 
          array (
            0 => 'SPORT GREY',
            1 => '#97999b',
            2 => 25457,
          ),
          6 => 
          array (
            0 => 'LIGHT PINK',
            1 => '#e4c6d4',
            2 => 25448,
          ),
          7 => 
          array (
            0 => 'MILITARY GREEN',
            1 => '#228b22',
            2 => 25466,
          ),
          8 => 
          array (
            0 => 'MAROON',
            1 => '#5b2b42',
            2 => 25449,
          ),
          9 => 
          array (
            0 => 'RED',
            1 => '#5b2b42',
            2 => 25453,
          ),
          10 => 
          array (
            0 => 'ROYAL BLUE',
            1 => '#5b2b42',
            2 => 25623,
          ),
          11 => 
          array (
            0 => 'ASH GREY',
            1 => '#5b2b42',
            2 => 79258,
          ),
        ),
        'XL' => 
        array (
          0 => 
          array (
            0 => 'ORANGE',
            1 => '#f75f36',
            2 => 25482,
          ),
          1 => 
          array (
            0 => 'BLACK',
            1 => '#000000',
            2 => 25490,
          ),
          2 => 
          array (
            0 => 'WHITE',
            1 => '#ffffff',
            2 => 25489,
          ),
          3 => 
          array (
            0 => 'FOREST GREEN',
            1 => '#228B22',
            2 => 25493,
          ),
          4 => 
          array (
            0 => 'SAND',
            1 => '#cabfad',
            2 => 25487,
          ),
          5 => 
          array (
            0 => 'SPORT GREY',
            1 => '#97999b',
            2 => 25488,
          ),
          6 => 
          array (
            0 => 'LIGHT PINK',
            1 => '#e4c6d4',
            2 => 25479,
          ),
          7 => 
          array (
            0 => 'MILITARY GREEN',
            1 => '#228b22',
            2 => 25497,
          ),
          8 => 
          array (
            0 => 'MAROON',
            1 => '#5b2b42',
            2 => 25480,
          ),
          9 => 
          array (
            0 => 'RED',
            1 => '#5b2b42',
            2 => 25484,
          ),
          10 => 
          array (
            0 => 'ROYAL BLUE',
            1 => '#5b2b42',
            2 => 25626,
          ),
          11 => 
          array (
            0 => 'ASH GREY',
            1 => '#5b2b42',
            2 => 79259,
          ),
        ),
        '2XL' => 
        array (
          0 => 
          array (
            0 => 'ORANGE',
            1 => '#f75f36',
            2 => 25513,
          ),
          1 => 
          array (
            0 => 'BLACK',
            1 => '#000000',
            2 => 25521,
          ),
          2 => 
          array (
            0 => 'WHITE',
            1 => '#ffffff',
            2 => 25520,
          ),
          3 => 
          array (
            0 => 'FOREST GREEN',
            1 => '#228B22',
            2 => 25524,
          ),
          4 => 
          array (
            0 => 'SAND',
            1 => '#cabfad',
            2 => 25518,
          ),
          5 => 
          array (
            0 => 'SPORT GREY',
            1 => '#97999b',
            2 => 25519,
          ),
          6 => 
          array (
            0 => 'LIGHT PINK',
            1 => '#e4c6d4',
            2 => 25510,
          ),
          7 => 
          array (
            0 => 'MILITARY GREEN',
            1 => '#228b22',
            2 => 25528,
          ),
          8 => 
          array (
            0 => 'MAROON',
            1 => '#5b2b42',
            2 => 25511,
          ),
          9 => 
          array (
            0 => 'RED',
            1 => '#5b2b42',
            2 => 25515,
          ),
          10 => 
          array (
            0 => 'ROYAL BLUE',
            1 => '#5b2b42',
            2 => 25627,
          ),
          11 => 
          array (
            0 => 'ASH GREY',
            1 => '#5b2b42',
            2 => 79260,
          ),
        ),
        '3XL' => 
        array (
          0 => 
          array (
            0 => 'ORANGE',
            1 => '#f75f36',
            2 => 25544,
          ),
          1 => 
          array (
            0 => 'BLACK',
            1 => '#000000',
            2 => 25552,
          ),
          2 => 
          array (
            0 => 'WHITE',
            1 => '#ffffff',
            2 => 25551,
          ),
          3 => 
          array (
            0 => 'FOREST GREEN',
            1 => '#228B22',
            2 => 25555,
          ),
          4 => 
          array (
            0 => 'SAND',
            1 => '#cabfad',
            2 => 25549,
          ),
          5 => 
          array (
            0 => 'SPORT GREY',
            1 => '#97999b',
            2 => 25550,
          ),
          6 => 
          array (
            0 => 'LIGHT PINK',
            1 => '#e4c6d4',
            2 => 25541,
          ),
          7 => 
          array (
            0 => 'MILITARY GREEN',
            1 => '#228b22',
            2 => 25406,
          ),
          8 => 
          array (
            0 => 'MAROON',
            1 => '#5b2b42',
            2 => 25542,
          ),
          9 => 
          array (
            0 => 'RED',
            1 => '#5b2b42',
            2 => 25546,
          ),
          10 => 
          array (
            0 => 'ROYAL BLUE',
            1 => '#5b2b42',
            2 => 25628,
          ),
          11 => 
          array (
            0 => 'ASH GREY',
            1 => '#5b2b42',
            2 => 79261,
          ),
        ),
        '4XL' => 
        array (
          0 => 
          array (
            0 => 'ORANGE',
            1 => '#f75f36',
            2 => 25575,
          ),
          1 => 
          array (
            0 => 'BLACK',
            1 => '#000000',
            2 => 25583,
          ),
          2 => 
          array (
            0 => 'WHITE',
            1 => '#ffffff',
            2 => 25582,
          ),
          3 => 
          array (
            0 => 'FOREST GREEN',
            1 => '#228B22',
            2 => 25586,
          ),
          4 => 
          array (
            0 => 'SPORT GREY',
            1 => '#97999b',
            2 => 25581,
          ),
          5 => 
          array (
            0 => 'RED',
            1 => '#5b2b42',
            2 => 25577,
          ),
          6 => 
          array (
            0 => 'ROYAL BLUE',
            1 => '#5b2b42',
            2 => 25629,
          ),
          7 => 
          array (
            0 => 'ASH GREY',
            1 => '#5b2b42',
            2 => 79262,
          ),
        ),
        '5XL' => 
        array (
          0 => 
          array (
            0 => 'ORANGE',
            1 => '#f75f36',
            2 => 25606,
          ),
          1 => 
          array (
            0 => 'BLACK',
            1 => '#000000',
            2 => 25614,
          ),
          2 => 
          array (
            0 => 'WHITE',
            1 => '#ffffff',
            2 => 25613,
          ),
          3 => 
          array (
            0 => 'FOREST GREEN',
            1 => '#228B22',
            2 => 25617,
          ),
          4 => 
          array (
            0 => 'SPORT GREY',
            1 => '#97999b',
            2 => 25612,
          ),
          5 => 
          array (
            0 => 'RED',
            1 => '#5b2b42',
            2 => 25608,
          ),
          6 => 
          array (
            0 => 'ROYAL BLUE',
            1 => '#5b2b42',
            2 => 25630,
          ),
          7 => 
          array (
            0 => 'ASH GREY',
            1 => '#5b2b42',
            2 => 79657,
          ),
        ),
      ),
      'Bella-Canvas-T-Shirt' => 
      array (
        'S' => 
        array (
          0 => 
          array (
            0 => 'BLACK',
            1 => '#191516',
            2 => 121262,
          ),
          1 => 
          array (
            0 => 'WHITE',
            1 => '#f2f1f6',
            2 => 121022,
          ),
          2 => 
          array (
            0 => 'DARK GREY HEATHER',
            1 => '#333333',
            2 => 181482,
          ),
          3 => 
          array (
            0 => 'NATURAL',
            1 => '#dcd7c4',
            2 => 183882,
          ),
        ),
        'M' => 
        array (
          0 => 
          array (
            0 => 'BLACK',
            1 => '#191516',
            2 => 121252,
          ),
          1 => 
          array (
            0 => 'WHITE',
            1 => '#f2f1f6',
            2 => 121012,
          ),
          2 => 
          array (
            0 => 'DARK GREY HEATHER',
            1 => '#333333',
            2 => 181492,
          ),
          3 => 
          array (
            0 => 'NATURAL',
            1 => '#dcd7c4',
            2 => 183892,
          ),
        ),
        'L' => 
        array (
          0 => 
          array (
            0 => 'BLACK',
            1 => '#191516',
            2 => 121242,
          ),
          1 => 
          array (
            0 => 'WHITE',
            1 => '#f2f1f6',
            2 => 121002,
          ),
          2 => 
          array (
            0 => 'DARK GREY HEATHER',
            1 => '#333333',
            2 => 181502,
          ),
          3 => 
          array (
            0 => 'NATURAL',
            1 => '#dcd7c4',
            2 => 183902,
          ),
        ),
        'XL' => 
        array (
          0 => 
          array (
            0 => 'BLACK',
            1 => '#191516',
            2 => 121272,
          ),
          1 => 
          array (
            0 => 'WHITE',
            1 => '#f2f1f6',
            2 => 121032,
          ),
          2 => 
          array (
            0 => 'DARK GREY HEATHER',
            1 => '#333333',
            2 => 181512,
          ),
          3 => 
          array (
            0 => 'NATURAL',
            1 => '#dcd7c4',
            2 => 183912,
          ),
        ),
        '2XL' => 
        array (
          0 => 
          array (
            0 => 'BLACK',
            1 => '#191516',
            2 => 121282,
          ),
          1 => 
          array (
            0 => 'WHITE',
            1 => '#f2f1f6',
            2 => 121042,
          ),
          2 => 
          array (
            0 => 'DARK GREY HEATHER',
            1 => '#333333',
            2 => 181522,
          ),
          3 => 
          array (
            0 => 'NATURAL',
            1 => '#dcd7c4',
            2 => 183922,
          ),
        ),
        '3XL' => 
        array (
          0 => 
          array (
            0 => 'BLACK',
            1 => '#191516',
            2 => 121292,
          ),
          1 => 
          array (
            0 => 'WHITE',
            1 => '#f2f1f6',
            2 => 121052,
          ),
          2 => 
          array (
            0 => 'DARK GREY HEATHER',
            1 => '#333333',
            2 => 181532,
          ),
          3 => 
          array (
            0 => 'NATURAL',
            1 => '#dcd7c4',
            2 => 183932,
          ),
        ),
        '4XL' => 
        array (
          0 => 
          array (
            0 => 'BLACK',
            1 => '#191516',
            2 => 240392,
          ),
          1 => 
          array (
            0 => 'WHITE',
            1 => '#f2f1f6',
            2 => 240312,
          ),
          2 => 
          array (
            0 => 'DARK GREY HEATHER',
            1 => '#333333',
            2 => 181542,
          ),
          3 => 
          array (
            0 => 'NATURAL',
            1 => '#dcd7c4',
            2 => 239912,
          ),
        ),
        '5XL' => 
        array (
          0 => 
          array (
            0 => 'BLACK',
            1 => '#191516',
            2 => 241712,
          ),
          1 => 
          array (
            0 => 'WHITE',
            1 => '#f2f1f6',
            2 => 241642,
          ),
        ),
      ),
      'Comfort-T-Shirt' => 
      array (
        'S' => 
        array (
          0 => 
          array (
            0 => 'BLACK',
            1 => '#191516',
            2 => 121263,
          ),
          1 => 
          array (
            0 => 'WHITE',
            1 => '#f2f1f6',
            2 => 121023,
          ),
          2 => 
          array (
            0 => 'PEPPER',
            1 => '#474747',
            2 => 790463,
          ),
          3 => 
          array (
            0 => 'IVORY',
            1 => '#f0e6dc',
            2 => 789913,
          ),
        ),
        'M' => 
        array (
          0 => 
          array (
            0 => 'BLACK',
            1 => '#191516',
            2 => 121253,
          ),
          1 => 
          array (
            0 => 'WHITE',
            1 => '#f2f1f6',
            2 => 121013,
          ),
          2 => 
          array (
            0 => 'PEPPER',
            1 => '#474747',
            2 => 790473,
          ),
          3 => 
          array (
            0 => 'IVORY',
            1 => '#f0e6dc',
            2 => 789923,
          ),
        ),
        'L' => 
        array (
          0 => 
          array (
            0 => 'BLACK',
            1 => '#191516',
            2 => 121243,
          ),
          1 => 
          array (
            0 => 'WHITE',
            1 => '#f2f1f6',
            2 => 121003,
          ),
          2 => 
          array (
            0 => 'PEPPER',
            1 => '#474747',
            2 => 790483,
          ),
          3 => 
          array (
            0 => 'IVORY',
            1 => '#f0e6dc',
            2 => 789933,
          ),
        ),
        'XL' => 
        array (
          0 => 
          array (
            0 => 'BLACK',
            1 => '#191516',
            2 => 121273,
          ),
          1 => 
          array (
            0 => 'WHITE',
            1 => '#f2f1f6',
            2 => 121033,
          ),
          2 => 
          array (
            0 => 'PEPPER',
            1 => '#474747',
            2 => 790493,
          ),
          3 => 
          array (
            0 => 'IVORY',
            1 => '#f0e6dc',
            2 => 789943,
          ),
        ),
        '2XL' => 
        array (
          0 => 
          array (
            0 => 'BLACK',
            1 => '#191516',
            2 => 121283,
          ),
          1 => 
          array (
            0 => 'WHITE',
            1 => '#f2f1f6',
            2 => 121043,
          ),
          2 => 
          array (
            0 => 'PEPPER',
            1 => '#474747',
            2 => 790503,
          ),
          3 => 
          array (
            0 => 'IVORY',
            1 => '#f0e6dc',
            2 => 789953,
          ),
        ),
        '3XL' => 
        array (
          0 => 
          array (
            0 => 'BLACK',
            1 => '#191516',
            2 => 121293,
          ),
          1 => 
          array (
            0 => 'WHITE',
            1 => '#f2f1f6',
            2 => 121053,
          ),
          2 => 
          array (
            0 => 'PEPPER',
            1 => '#474747',
            2 => 791553,
          ),
          3 => 
          array (
            0 => 'IVORY',
            1 => '#f0e6dc',
            2 => 791423,
          ),
        ),
        '4XL' => 
        array (
          0 => 
          array (
            0 => 'BLACK',
            1 => '#191516',
            2 => 240393,
          ),
          1 => 
          array (
            0 => 'WHITE',
            1 => '#f2f1f6',
            2 => 240313,
          ),
          2 => 
          array (
            0 => 'PEPPER',
            1 => '#474747',
            2 => 241543,
          ),
          3 => 
          array (
            0 => 'IVORY',
            1 => '#f0e6dc',
            2 => 241583,
          ),
        ),
      ),
      'Tote Bag' => 
      array (
        'Normal' => 
        array (
          0 => 
          array (
            0 => 'BLACK',
            1 => '#191516',
            2 => 87900,
          ),
          1 => 
          array (
            0 => 'NATURAL',
            1 => '#dcd7c4',
            2 => 87901,
          ),
        ),
      ),
      'Tank Top G2200' => 
      array (
        'S' => 
        array (
          0 => 
          array (
            0 => 'BLACK',
            1 => '#191516',
            2 => 76974,
          ),
          1 => 
          array (
            0 => 'WHITE',
            1 => '#f2f1f6',
            2 => 76973,
          ),
          2 => 
          array (
            0 => 'SPORT GREY',
            1 => '#b5b2b9',
            2 => 76972,
          ),
          3 => 
          array (
            0 => 'NAVY',
            1 => '#252d42',
            2 => 76969,
          ),
        ),
        'M' => 
        array (
          0 => 
          array (
            0 => 'BLACK',
            1 => '#191516',
            2 => 76980,
          ),
          1 => 
          array (
            0 => 'WHITE',
            1 => '#f2f1f6',
            2 => 76979,
          ),
          2 => 
          array (
            0 => 'SPORT GREY',
            1 => '#b5b2b9',
            2 => 76978,
          ),
          3 => 
          array (
            0 => 'NAVY',
            1 => '#252d42',
            2 => 76975,
          ),
        ),
        'L' => 
        array (
          0 => 
          array (
            0 => 'BLACK',
            1 => '#191516',
            2 => 76986,
          ),
          1 => 
          array (
            0 => 'WHITE',
            1 => '#f2f1f6',
            2 => 76985,
          ),
          2 => 
          array (
            0 => 'SPORT GREY',
            1 => '#b5b2b9',
            2 => 76984,
          ),
          3 => 
          array (
            0 => 'NAVY',
            1 => '#252d42',
            2 => 76981,
          ),
        ),
        'XL' => 
        array (
          0 => 
          array (
            0 => 'BLACK',
            1 => '#191516',
            2 => 76992,
          ),
          1 => 
          array (
            0 => 'WHITE',
            1 => '#f2f1f6',
            2 => 76991,
          ),
          2 => 
          array (
            0 => 'SPORT GREY',
            1 => '#b5b2b9',
            2 => 76990,
          ),
          3 => 
          array (
            0 => 'NAVY',
            1 => '#252d42',
            2 => 76987,
          ),
        ),
        '2XL' => 
        array (
          0 => 
          array (
            0 => 'BLACK',
            1 => '#191516',
            2 => 76998,
          ),
          1 => 
          array (
            0 => 'WHITE',
            1 => '#f2f1f6',
            2 => 76997,
          ),
          2 => 
          array (
            0 => 'SPORT GREY',
            1 => '#b5b2b9',
            2 => 76996,
          ),
          3 => 
          array (
            0 => 'NAVY',
            1 => '#252d42',
            2 => 76993,
          ),
        ),
        '3XL' => 
        array (
          0 => 
          array (
            0 => 'BLACK',
            1 => '#191516',
            2 => 84118,
          ),
          1 => 
          array (
            0 => 'WHITE',
            1 => '#f2f1f6',
            2 => 84128,
          ),
          2 => 
          array (
            0 => 'SPORT GREY',
            1 => '#b5b2b9',
            2 => 84138,
          ),
          3 => 
          array (
            0 => 'NAVY',
            1 => '#252d42',
            2 => 84168,
          ),
        ),
      ),
    ),
  ),
  'general' => 
  array (
    0 => 
    array (
      'name' => 'Require SKU in Stock on Order',
      'code' => 'REQUIRE_SKU_IN_STOCK_ON_ORDER',
    ),
    1 => 
    array (
      'name' => 'Dollar to Vietnamese currency exchange rate',
      'code' => 'DOLLAR_EXCHANGE_RATE',
    ),
    2 => 
    array (
      'name' => 'Seller can see production history',
      'code' => 'SELLER_SEE_PRODUCTION_HISTORY',
    ),
    3 => 
    array (
      'name' => 'ViettelPost from information',
      'code' => 'VIETTELPOST_FROM_INFO',
    ),
    4 => 
    array (
      'name' => 'SKU low stock Threshold',
      'code' => 'SKU_LOW_STOCK_THRESHOLD',
    ),
  ),
  'hashing' => 
  array (
    'driver' => 'bcrypt',
    'bcrypt' => 
    array (
      'rounds' => 10,
    ),
    'argon' => 
    array (
      'memory' => 65536,
      'threads' => 1,
      'time' => 4,
    ),
  ),
  'logging' => 
  array (
    'default' => 'stack',
    'deprecations' => NULL,
    'channels' => 
    array (
      'stack' => 
      array (
        'driver' => 'stack',
        'channels' => 
        array (
          0 => 'single',
          1 => 'telegram',
        ),
        'ignore_exceptions' => false,
      ),
      'telegram' => 
      array (
        'driver' => 'monolog',
        'handler' => 'Monolog\\Handler\\NullHandler',
        'level' => 'warning',
        'handler_with' => 
        array (
          'apiKey' => NULL,
          'channel' => NULL,
        ),
      ),
      'single' => 
      array (
        'driver' => 'single',
        'path' => '/var/www/html/app/storage/logs/laravel.log',
        'level' => 'debug',
      ),
      'factory' => 
      array (
        'driver' => 'single',
        'path' => '/var/www/html/app/storage/logs/factory.log',
        'level' => 'debug',
      ),
      'import_seller_orders' => 
      array (
        'driver' => 'single',
        'path' => '/var/www/html/app/storage/logs/import_seller_orders.log',
        'level' => 'debug',
      ),
      'seller_order' => 
      array (
        'driver' => 'single',
        'path' => '/var/www/html/app/storage/logs/seller_order.log',
        'level' => 'debug',
      ),
      'restore_stock_batch' => 
      array (
        'driver' => 'single',
        'path' => '/var/www/html/app/storage/logs/restore_stock_batch.log',
        'level' => 'debug',
      ),
      'stock_sku' => 
      array (
        'driver' => 'single',
        'path' => '/var/www/html/app/storage/logs/stock_sku.log',
        'level' => 'debug',
      ),
      'daily' => 
      array (
        'driver' => 'daily',
        'path' => '/var/www/html/app/storage/logs/laravel.log',
        'level' => 'debug',
        'days' => 14,
      ),
      'slack' => 
      array (
        'driver' => 'slack',
        'url' => NULL,
        'username' => 'Laravel Log',
        'emoji' => ':boom:',
        'level' => 'debug',
      ),
      'papertrail' => 
      array (
        'driver' => 'monolog',
        'level' => 'debug',
        'handler' => 'Monolog\\Handler\\SyslogUdpHandler',
        'handler_with' => 
        array (
          'host' => NULL,
          'port' => NULL,
        ),
      ),
      'stderr' => 
      array (
        'driver' => 'monolog',
        'level' => 'debug',
        'handler' => 'Monolog\\Handler\\StreamHandler',
        'formatter' => NULL,
        'with' => 
        array (
          'stream' => 'php://stderr',
        ),
      ),
      'syslog' => 
      array (
        'driver' => 'syslog',
        'level' => 'debug',
      ),
      'errorlog' => 
      array (
        'driver' => 'errorlog',
        'level' => 'debug',
      ),
      'null' => 
      array (
        'driver' => 'monolog',
        'handler' => 'Monolog\\Handler\\NullHandler',
      ),
      'emergency' => 
      array (
        'path' => '/var/www/html/app/storage/logs/laravel.log',
      ),
    ),
  ),
  'mail' => 
  array (
    'default' => 'smtp',
    'mailers' => 
    array (
      'smtp' => 
      array (
        'transport' => 'smtp',
        'host' => 'mailhog',
        'port' => '1025',
        'encryption' => NULL,
        'username' => NULL,
        'password' => NULL,
        'timeout' => NULL,
        'auth_mode' => NULL,
      ),
      'ses' => 
      array (
        'transport' => 'ses',
      ),
      'mailgun' => 
      array (
        'transport' => 'mailgun',
      ),
      'postmark' => 
      array (
        'transport' => 'postmark',
      ),
      'sendmail' => 
      array (
        'transport' => 'sendmail',
        'path' => '/usr/sbin/sendmail -t -i',
      ),
      'log' => 
      array (
        'transport' => 'log',
        'channel' => NULL,
      ),
      'array' => 
      array (
        'transport' => 'array',
      ),
      'failover' => 
      array (
        'transport' => 'failover',
        'mailers' => 
        array (
          0 => 'smtp',
          1 => 'log',
        ),
      ),
    ),
    'from' => 
    array (
      'address' => NULL,
      'name' => 'Laravel',
    ),
    'markdown' => 
    array (
      'theme' => 'default',
      'paths' => 
      array (
        0 => '/var/www/html/app/resources/views/vendor/mail',
      ),
    ),
  ),
  'permissions' => 
  array (
    0 => 
    array (
      'name' => 'Factory Order',
      'code' => 'factory_order',
      'actions' => 
      array (
        0 => 
        array (
          'name' => 'Read',
          'code' => 'read',
        ),
        1 => 
        array (
          'name' => 'Next Order Step',
          'code' => 'next_order_step',
        ),
        2 => 
        array (
          'name' => 'View All Columns of Order Table',
          'code' => 'view_all_columns',
        ),
        3 => 
        array (
          'name' => 'View Order Code',
          'code' => 'view_order_code',
        ),
        4 => 
        array (
          'name' => 'View Product Info',
          'code' => 'view_product_info',
        ),
        5 => 
        array (
          'name' => 'View Status',
          'code' => 'view_status',
        ),
        6 => 
        array (
          'name' => 'View Production Line',
          'code' => 'view_production_line',
        ),
        7 => 
        array (
          'name' => 'View Payment Info',
          'code' => 'view_payment_info',
        ),
        8 => 
        array (
          'name' => 'View Seller Info',
          'code' => 'view_seller_info',
        ),
        9 => 
        array (
          'name' => 'View Customer Info',
          'code' => 'view_customer_info',
        ),
        10 => 
        array (
          'name' => 'View Shipping Label',
          'code' => 'view_shipping_label',
        ),
        11 => 
        array (
          'name' => 'View Total Amount',
          'code' => 'view_total_amount',
        ),
        12 => 
        array (
          'name' => 'View Production Expect Time',
          'code' => 'view_production_expect_time',
        ),
        13 => 
        array (
          'name' => 'View Created At',
          'code' => 'view_created_at',
        ),
        14 => 
        array (
          'name' => 'Batch Assign Design',
          'code' => 'batch_assign_design',
        ),
        15 => 
        array (
          'name' => 'Assign Factory',
          'code' => 'assign_factory',
        ),
        16 => 
        array (
          'name' => 'Print Stamp',
          'code' => 'print_stamp',
        ),
        17 => 
        array (
          'name' => 'Export Wood Order',
          'code' => 'export_wood_order',
        ),
        18 => 
        array (
          'name' => 'Bulk Update Status',
          'code' => 'bulk_update_status',
        ),
        19 => 
        array (
          'name' => 'Download DST',
          'code' => 'download_dst',
        ),
        20 => 
        array (
          'name' => 'Download EMB',
          'code' => 'download_emb',
        ),
        21 => 
        array (
          'name' => 'Update Status',
          'code' => 'update_status',
        ),
        22 => 
        array (
          'name' => 'View Order Detail Price',
          'code' => 'view_order_detail_price',
        ),
        23 => 
        array (
          'name' => 'Update Design',
          'code' => 'update_design',
        ),
        24 => 
        array (
          'name' => 'Order Design',
          'code' => 'order_design',
        ),
        25 => 
        array (
          'name' => 'Confirm Design',
          'code' => 'confirm_design',
        ),
        26 => 
        array (
          'name' => 'Refund Order',
          'code' => 'refund_order',
        ),
        27 => 
        array (
          'name' => 'Cancel Order',
          'code' => 'cancel_order',
        ),
        28 => 
        array (
          'name' => 'Update Shipping Address',
          'code' => 'update_shipping_address',
        ),
        29 => 
        array (
          'name' => 'Buy Shipping Label',
          'code' => 'buy_shipping_label',
        ),
      ),
    ),
    1 => 
    array (
      'name' => 'Shipping Rate',
      'code' => 'shipping_rate',
      'actions' => 
      array (
        0 => 
        array (
          'name' => 'Read',
          'code' => 'read',
        ),
        1 => 
        array (
          'name' => 'Create',
          'code' => 'create',
        ),
        2 => 
        array (
          'name' => 'Update',
          'code' => 'update',
        ),
      ),
    ),
    2 => 
    array (
      'name' => 'Shipping Label',
      'code' => 'shipping_label',
      'actions' => 
      array (
        0 => 
        array (
          'name' => 'Read',
          'code' => 'read',
        ),
        1 => 
        array (
          'name' => 'Create',
          'code' => 'create',
        ),
        2 => 
        array (
          'name' => 'Print',
          'code' => 'print',
        ),
      ),
    ),
  ),
  'queue' => 
  array (
    'default' => 'database',
    'connections' => 
    array (
      'sync' => 
      array (
        'driver' => 'sync',
      ),
      'database' => 
      array (
        'driver' => 'database',
        'table' => 'jobs',
        'queue' => 'default',
        'retry_after' => 90,
        'after_commit' => false,
      ),
      'beanstalkd' => 
      array (
        'driver' => 'beanstalkd',
        'host' => 'localhost',
        'queue' => 'default',
        'retry_after' => 90,
        'block_for' => 0,
        'after_commit' => false,
      ),
      'sqs' => 
      array (
        'driver' => 'sqs',
        'key' => '',
        'secret' => '',
        'prefix' => 'https://sqs.us-east-1.amazonaws.com/your-account-id',
        'queue' => 'default',
        'suffix' => NULL,
        'region' => 'us-east-1',
        'after_commit' => false,
      ),
      'redis' => 
      array (
        'driver' => 'redis',
        'connection' => 'default',
        'queue' => 'default',
        'retry_after' => 90,
        'block_for' => NULL,
        'after_commit' => false,
      ),
    ),
    'failed' => 
    array (
      'driver' => 'database-uuids',
      'database' => 'mysql',
      'table' => 'failed_jobs',
    ),
  ),
  'sanctum' => 
  array (
    'stateful' => 
    array (
      0 => 'localhost',
      1 => 'localhost:3000',
      2 => '127.0.0.1',
      3 => '127.0.0.1:8000',
      4 => '::1',
      5 => 'localhost',
    ),
    'guard' => 
    array (
      0 => 'web',
    ),
    'expiration' => NULL,
    'middleware' => 
    array (
      'verify_csrf_token' => 'App\\Http\\Middleware\\VerifyCsrfToken',
      'encrypt_cookies' => 'App\\Http\\Middleware\\EncryptCookies',
    ),
  ),
  'services' => 
  array (
    'mailgun' => 
    array (
      'domain' => NULL,
      'secret' => NULL,
      'endpoint' => 'api.mailgun.net',
    ),
    'postmark' => 
    array (
      'token' => NULL,
    ),
    'ses' => 
    array (
      'key' => '',
      'secret' => '',
      'region' => 'us-east-1',
    ),
    '17track' => 
    array (
      'api_key' => NULL,
    ),
  ),
  'session' => 
  array (
    'driver' => 'file',
    'lifetime' => '120',
    'expire_on_close' => false,
    'encrypt' => false,
    'files' => '/var/www/html/app/storage/framework/sessions',
    'connection' => NULL,
    'table' => 'sessions',
    'store' => NULL,
    'lottery' => 
    array (
      0 => 2,
      1 => 100,
    ),
    'cookie' => 'laravel_session',
    'path' => '/',
    'domain' => NULL,
    'secure' => NULL,
    'http_only' => true,
    'same_site' => 'lax',
  ),
  'shipping_providers' => 
  array (
    0 => 
    array (
      'code' => 'fastway',
      'name' => 'FastWay',
      'api_host' => 'https://sandbox.fastway.co',
      'api_key' => '67430137d024c1ed55def0dc352793a3fbfe1347a26f53d0299f7db53a6fe8d2',
      'secret_key' => '',
    ),
    1 => 
    array (
      'code' => 'multrans',
      'name' => 'Multrans',
      'api_host' => 'https://api.multransglobalmail.com',
      'api_key' => '5g694o5d1ajkp5ucdd9dqh5069',
      'secret_key' => 'ksamlihsrfibeq9v9iegfegj9mt97toff7gnrgmjbga40n2q3uj',
    ),
    2 => 
    array (
      'code' => 'gke',
      'name' => 'GKE',
      'api_host' => 'https://oms.gkelogistics.com/its-api/cs/api',
      'api_key' => '47608e3aca7c41d6af9c454cfa29fd60',
      'secret_key' => 'a10ad09fdbfe4478b111894099d2ef4e',
    ),
    3 => 
    array (
      'code' => 'viettel_post',
      'name' => 'Viettel Post',
      'api_host' => 'https://oms.gkelogistics.com/its-api/cs/api',
      'api_key' => '47608e3aca7c41d6af9c454cfa29fd60',
      'secret_key' => 'a10ad09fdbfe4478b111894099d2ef4e',
    ),
  ),
  'telegram' => 
  array (
    'bots' => 
    array (
      'topup' => 
      array (
        'token' => '7696449201:AAFH4c_zoBqy5NquJENySkMMTB2WLhIVwkk',
        'chat_ids' => 
        array (
          0 => '-4826152854',
        ),
      ),
    ),
  ),
  'telescope' => 
  array (
    'domain' => NULL,
    'path' => 'telescope',
    'driver' => 'database',
    'storage' => 
    array (
      'database' => 
      array (
        'connection' => 'mysql',
        'chunk' => 1000,
      ),
    ),
    'enabled' => true,
    'middleware' => 
    array (
      0 => 'web',
      1 => 'Laravel\\Telescope\\Http\\Middleware\\Authorize',
    ),
    'only_paths' => 
    array (
    ),
    'ignore_paths' => 
    array (
      0 => 'livewire*',
      1 => 'nova-api*',
      2 => 'pulse*',
    ),
    'ignore_commands' => 
    array (
    ),
    'watchers' => 
    array (
      'Laravel\\Telescope\\Watchers\\BatchWatcher' => false,
      'Laravel\\Telescope\\Watchers\\CacheWatcher' => 
      array (
        'enabled' => false,
        'hidden' => 
        array (
        ),
      ),
      'Laravel\\Telescope\\Watchers\\ClientRequestWatcher' => false,
      'Laravel\\Telescope\\Watchers\\CommandWatcher' => 
      array (
        'enabled' => false,
        'ignore' => 
        array (
        ),
      ),
      'Laravel\\Telescope\\Watchers\\DumpWatcher' => 
      array (
        'enabled' => false,
        'always' => false,
      ),
      'Laravel\\Telescope\\Watchers\\EventWatcher' => 
      array (
        'enabled' => true,
        'ignore' => 
        array (
        ),
      ),
      'Laravel\\Telescope\\Watchers\\ExceptionWatcher' => false,
      'Laravel\\Telescope\\Watchers\\GateWatcher' => 
      array (
        'enabled' => false,
        'ignore_abilities' => 
        array (
        ),
        'ignore_packages' => true,
        'ignore_paths' => 
        array (
        ),
      ),
      'Laravel\\Telescope\\Watchers\\JobWatcher' => false,
      'Laravel\\Telescope\\Watchers\\LogWatcher' => 
      array (
        'enabled' => false,
        'level' => 'error',
      ),
      'Laravel\\Telescope\\Watchers\\MailWatcher' => false,
      'Laravel\\Telescope\\Watchers\\ModelWatcher' => 
      array (
        'enabled' => false,
        'events' => 
        array (
          0 => 'eloquent.*',
        ),
        'hydrations' => true,
      ),
      'Laravel\\Telescope\\Watchers\\NotificationWatcher' => false,
      'Laravel\\Telescope\\Watchers\\QueryWatcher' => 
      array (
        'enabled' => false,
        'ignore_packages' => true,
        'ignore_paths' => 
        array (
        ),
        'slow' => 100,
      ),
      'Laravel\\Telescope\\Watchers\\RedisWatcher' => false,
      'Laravel\\Telescope\\Watchers\\RequestWatcher' => 
      array (
        'enabled' => false,
        'size_limit' => 64,
        'ignore_http_methods' => 
        array (
        ),
        'ignore_status_codes' => 
        array (
        ),
      ),
      'Laravel\\Telescope\\Watchers\\ScheduleWatcher' => false,
      'Laravel\\Telescope\\Watchers\\ViewWatcher' => false,
    ),
  ),
  'view' => 
  array (
    'paths' => 
    array (
      0 => '/var/www/html/app/resources/views',
    ),
    'compiled' => '/var/www/html/app/storage/framework/views',
  ),
  'websockets' => 
  array (
    'dashboard' => 
    array (
      'port' => '6001',
    ),
    'apps' => 
    array (
      0 => 
      array (
        'id' => 'local',
        'name' => 'Laravel',
        'key' => 'local',
        'secret' => 'local',
        'path' => NULL,
        'host' => '127.0.0.1',
        'capacity' => NULL,
        'enable_client_messages' => false,
        'enable_statistics' => true,
      ),
    ),
    'app_provider' => 'BeyondCode\\LaravelWebSockets\\Apps\\ConfigAppProvider',
    'allowed_origins' => 
    array (
    ),
    'max_request_size_in_kb' => 250,
    'path' => 'websockets',
    'middleware' => 
    array (
      0 => 'web',
      1 => 'BeyondCode\\LaravelWebSockets\\Dashboard\\Http\\Middleware\\Authorize',
    ),
    'statistics' => 
    array (
      'model' => 'BeyondCode\\LaravelWebSockets\\Statistics\\Models\\WebSocketsStatisticsEntry',
      'logger' => 'BeyondCode\\LaravelWebSockets\\Statistics\\Logger\\HttpStatisticsLogger',
      'interval_in_seconds' => 60,
      'delete_statistics_older_than_days' => 60,
      'perform_dns_lookup' => false,
    ),
    'ssl' => 
    array (
      'local_cert' => NULL,
      'local_pk' => NULL,
      'passphrase' => NULL,
      'verify_peer' => false,
      'allow_self_signed' => true,
    ),
    'channel_manager' => 'BeyondCode\\LaravelWebSockets\\WebSockets\\Channels\\ChannelManagers\\ArrayChannelManager',
  ),
  'dompdf' => 
  array (
    'show_warnings' => false,
    'public_path' => NULL,
    'convert_entities' => true,
    'options' => 
    array (
      'font_dir' => '/var/www/html/app/storage/fonts',
      'font_cache' => '/var/www/html/app/storage/fonts',
      'temp_dir' => '/tmp',
      'chroot' => '/var/www/html/app',
      'allowed_protocols' => 
      array (
        'file://' => 
        array (
          'rules' => 
          array (
          ),
        ),
        'http://' => 
        array (
          'rules' => 
          array (
          ),
        ),
        'https://' => 
        array (
          'rules' => 
          array (
          ),
        ),
      ),
      'log_output_file' => NULL,
      'enable_font_subsetting' => false,
      'pdf_backend' => 'CPDF',
      'default_media_type' => 'screen',
      'default_paper_size' => 'a4',
      'default_paper_orientation' => 'portrait',
      'default_font' => 'serif',
      'dpi' => 96,
      'enable_php' => false,
      'enable_javascript' => true,
      'enable_remote' => true,
      'font_height_ratio' => 1.1,
      'enable_html5_parser' => true,
    ),
  ),
  'flare' => 
  array (
    'key' => NULL,
    'reporting' => 
    array (
      'anonymize_ips' => true,
      'collect_git_information' => false,
      'report_queries' => true,
      'maximum_number_of_collected_queries' => 200,
      'report_query_bindings' => true,
      'report_view_data' => true,
      'grouping_type' => NULL,
      'report_logs' => true,
      'maximum_number_of_collected_logs' => 200,
      'censor_request_body_fields' => 
      array (
        0 => 'password',
      ),
    ),
    'send_logs_as_events' => true,
    'censor_request_body_fields' => 
    array (
      0 => 'password',
    ),
  ),
  'ignition' => 
  array (
    'editor' => 'phpstorm',
    'theme' => 'light',
    'enable_share_button' => true,
    'register_commands' => false,
    'ignored_solution_providers' => 
    array (
      0 => 'Facade\\Ignition\\SolutionProviders\\MissingPackageSolutionProvider',
    ),
    'enable_runnable_solutions' => NULL,
    'remote_sites_path' => '',
    'local_sites_path' => '',
    'housekeeping_endpoint_prefix' => '_ignition',
  ),
  'image' => 
  array (
    'driver' => 'gd',
  ),
  'tinker' => 
  array (
    'commands' => 
    array (
    ),
    'alias' => 
    array (
    ),
    'dont_alias' => 
    array (
      0 => 'App\\Nova',
    ),
  ),
);

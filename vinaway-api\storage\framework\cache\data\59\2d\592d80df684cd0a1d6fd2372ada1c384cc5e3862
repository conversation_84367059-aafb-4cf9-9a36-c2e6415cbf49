1754042837O:16:"App\Models\Staff":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:6:"staffs";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:5:"roles";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:15:{s:2:"id";i:2;s:4:"name";s:13:"Admin Vinaway";s:6:"avatar";N;s:5:"email";s:24:"<EMAIL>";s:8:"password";s:60:"$2y$10$agx5zACTgb3STYZQC.jCJehSK8xuG9uYBuFcqbAkAFZi9V7tcMrlS";s:10:"factory_id";i:-1;s:13:"staff_role_id";i:-1;s:6:"status";i:1;s:14:"remember_token";N;s:10:"creator_id";i:0;s:10:"updater_id";N;s:10:"deleter_id";N;s:10:"created_at";s:19:"2025-04-20 19:24:52";s:10:"updated_at";s:19:"2025-04-20 19:24:52";s:10:"deleted_at";N;}s:11:" * original";a:15:{s:2:"id";i:2;s:4:"name";s:13:"Admin Vinaway";s:6:"avatar";N;s:5:"email";s:24:"<EMAIL>";s:8:"password";s:60:"$2y$10$agx5zACTgb3STYZQC.jCJehSK8xuG9uYBuFcqbAkAFZi9V7tcMrlS";s:10:"factory_id";i:-1;s:13:"staff_role_id";i:-1;s:6:"status";i:1;s:14:"remember_token";N;s:10:"creator_id";i:0;s:10:"updater_id";N;s:10:"deleter_id";N;s:10:"created_at";s:19:"2025-04-20 19:24:52";s:10:"updated_at";s:19:"2025-04-20 19:24:52";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:6:"status";s:7:"integer";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:5:"roles";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:2:{i:0;s:8:"password";i:1;s:14:"remember_token";}s:10:" * visible";a:0:{}s:11:" * fillable";a:7:{i:0;s:4:"name";i:1;s:5:"email";i:2;s:8:"password";i:3;s:10:"factory_id";i:4;s:13:"staff_role_id";i:5;s:6:"status";i:6;s:6:"avatar";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:20:" * rememberTokenName";s:14:"remember_token";s:9:" * filter";a:2:{i:0;s:4:"name";i:1;s:6:"status";}s:14:" * accessToken";O:35:"Laravel\Sanctum\PersonalAccessToken":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:22:"personal_access_tokens";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:9:{s:2:"id";i:667;s:14:"tokenable_type";s:16:"App\Models\Staff";s:12:"tokenable_id";i:2;s:4:"name";s:7:"Vinaway";s:5:"token";s:64:"05be2ad0911833cdbe3bca31587bb0642de68994e5b0b8afe5f488332362746c";s:9:"abilities";s:5:"["*"]";s:12:"last_used_at";N;s:10:"created_at";s:19:"2025-07-31 22:25:10";s:10:"updated_at";s:19:"2025-07-31 22:25:10";}s:11:" * original";a:9:{s:2:"id";i:667;s:14:"tokenable_type";s:16:"App\Models\Staff";s:12:"tokenable_id";i:2;s:4:"name";s:7:"Vinaway";s:5:"token";s:64:"05be2ad0911833cdbe3bca31587bb0642de68994e5b0b8afe5f488332362746c";s:9:"abilities";s:5:"["*"]";s:12:"last_used_at";N;s:10:"created_at";s:19:"2025-07-31 22:25:10";s:10:"updated_at";s:19:"2025-07-31 22:25:10";}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:9:"abilities";s:4:"json";s:12:"last_used_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:9:"tokenable";r:1;}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:1:{i:0;s:5:"token";}s:10:" * visible";a:0:{}s:11:" * fillable";a:3:{i:0;s:4:"name";i:1;s:5:"token";i:2;s:9:"abilities";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}
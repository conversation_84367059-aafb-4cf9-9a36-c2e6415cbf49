<?php

namespace App\Notifications;

use App\Models\StockSku;
use Illuminate\Notifications\Notification;

class LowStockNotification extends Notification
{
    public $stockSku;

    public $threshold;

    /**
     * Create a new notification instance.
     */
    public function __construct($stockSku, int $threshold)
    {
        $this->stockSku = $stockSku;
        $this->threshold = $threshold;
    }

    /**
     * Get the notification's delivery channels.
     */
    public function via($notifiable): array
    {
        return ['database'];
    }

    /**
     * Get the array representation of the notification.
     */
    public function toArray($notifiable): array
    {
        return [
            'type' => 'low_stock',
            'stock_sku_id' => $this->stockSku->id,
            'product_sku_id' => $this->stockSku->product_sku_id,
            'sku_name' => $this->stockSku->productSku->name,
            'stock_name' => $this->stockSku->stock->name,
            'current_quantity' => $this->stockSku->quantity,
            'threshold' => $this->threshold,
            'title' => "Low Stock Alert: {$this->stockSku->productSku->name}",
            'message' => "{$this->stockSku->productSku->name} in {$this->stockSku->stock->name} has only {$this->stockSku->quantity} units left (Threshold: {$this->threshold})",
            'severity' => $this->stockSku->quantity == 0 ? 'critical' : 'warning',
            'link' => "/factories/stock-skus?stock_id={$this->stockSku->stock_id}&product_id={$this->stockSku->product_id}&product_sku_id={$this->stockSku->product_sku_id}",
            'timestamp' => now()->toISOString(),
        ];
    }
}

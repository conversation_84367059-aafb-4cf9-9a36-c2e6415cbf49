<script setup>
import useFilter from '@/composables/useFilter'
import AddSKUsStockBatchTable from '~/views/pages/factories/stock-batches/AddSKUsStockBatchTable.vue'
import EditSKUsStockBatchTable from '~/views/pages/factories/stock-batches/EditSKUsStockBatchTable.vue'
import { VDateInput } from 'vuetify/labs/VDateInput'
import { formatDate } from '@helpers/DateHelper.js'
import { PRODUCT_SERVICE, PRODUCT_SERVICES, STOCK_BATCH_TYPE, STOCK_BATCH_TYPE_OPTIONS } from '@helpers/ConstantHelper.js'

const props = defineProps({
  modelValue: {
    type: Object,
    required: false,
    default: undefined,
  },
  stockBatchAction: {
    type: Object,
    required: false,
    default: {
      stock: null,
      type: null,
    }
  },
})

const { filter, updateOptions, callback } = useFilter({
  page: 1,
  limit: 25,
})

const { filter: filterSkus, updateOptions: updateOptionSkus, callback: callbackSku } = useFilter({
  page: 1,
  limit: 25,
})

const emit = defineEmits([
  'update',
  'update:modelValue',
])

const { data: sessionData } = useAuth()
const factoryIdOfStaff = sessionData.value?.user.factory_id

const headers = computed(() => [
  {
    title: 'Sku name',
    key: 'name',
    width: '20%',
  },
  {
    title: 'Add quantity',
    key: 'quantity',
    width: '15%',
    sortable: false
  },
  {
    title: 'Add price',
    key: 'unit_price',
    width: '15%',
    sortable: false
  },
  {
    title: 'Reason',
    key: 'reason',
    width: '30%',
    sortable: false
  },
  {
    title: 'Total Price',
    key: 'price',
    sortable: false
  },
].filter(Boolean))

const isDialogVisible = ref(false)
const cardRef = ref()

const compType = computed(() => props.modelValue ? 'Edit' : 'Add')

const isTypeImport = computed(() => form.type ? form.type === STOCK_BATCH_TYPE.IMPORT : props.modelValue?.type === STOCK_BATCH_TYPE.IMPORT)
const isTypeExport = computed(() => form.type ? form.type === STOCK_BATCH_TYPE.EXPORT : props.modelValue?.type === STOCK_BATCH_TYPE.EXPORT)
const isTypeTransfer = computed(() => form.type ? form.type === STOCK_BATCH_TYPE.TRANSFER : props.modelValue?.type === STOCK_BATCH_TYPE.TRANSFER)

const isDisplayTransferStockSelect = computed(() => (form.stock_id || props.modelValue?.stock.id) && isTypeTransfer.value)

const productSkus = ref([])
const totalProductSkus = ref(0)
const inputedSkuItemsImport = ref([])
const inputedSkuItemsImportEdit = ref([])

const skusForExport = ref([])
const totalSkusForExport = ref(0)
const inputedSkuItemsExport = ref([])
const inputedSkuItemsExportEdit = ref([])

const tableLoading = ref(false)
const totalQuantity = ref(0);
const totalPrice = ref(0);

const loading = reactive({
  status: false,
  message: null,
})

const refForm = ref()
const form = reactive({
  stock_id: props.modelValue?.stock.id,
  name: props.modelValue?.name,
  type: props.modelValue?.type,
  transfer_to_stock_id: props.modelValue?.transfer_to_stock_id,
  status: props.modelValue?.status,
  handle_date: new Date(props.modelValue?.handle_date),
  note: props.modelValue?.note,
  photos: props.modelValue?.photos,
  reason: props.modelValue?.reason
})
const isResetForm = ref(false)
const validateErrors = ref(null)

const onReset = () => {
  isDialogVisible.value = false
  isResetForm.value = true
  productSkus.value = []
  skusForExport.value = []
  totalProductSkus.value = 0
  totalSkusForExport.value = 0
  totalQuantity.value = 0
  totalPrice.value = 0
  // Reset inputed items to prevent data carry-over between different stock batches
  inputedSkuItemsImport.value = []
  inputedSkuItemsImportEdit.value = []
  inputedSkuItemsExport.value = []
  inputedSkuItemsExportEdit.value = []
  validateErrors.value = null
  emit('update:modelValue', null)
  nextTick(() => {
    refForm.value?.reset()
    refForm.value?.resetValidation()
    isResetForm.value = false
  })
}

const getAllSkus = async () => {
  const params = filter
  params.service = PRODUCT_SERVICES.PRODUCTION
  tableLoading.value = true
  const { data } = await useApi(`/product-skus/factory`, { params })
  productSkus.value = data?.value?.data.map(item => {
    if (inputedSkuItemsImport.value.length) {
      const inputedItem = inputedSkuItemsImport.value.find(inputedItem => inputedItem?.product_sku_id === item?.product_sku_id)
      if (inputedItem) return {...inputedItem}
    }
    return item
  })
  totalProductSkus.value = data?.value?.total
  tableLoading.value = false
}

const getSkusForExport = async (stockId) => {
  const params = filterSkus
  params.stock = stockId
  params.for_stock_batch = true
  tableLoading.value = true
  const { data } = await useApi(`/factory/${factoryIdOfStaff}/stock-skus`, { params })
  skusForExport.value = data?.value.data.map(item => {
    if (inputedSkuItemsExport.value.length) {
      const inputedItem = inputedSkuItemsExport.value.find(inputedItem => inputedItem?.product_sku_id === item?.product_sku_id)
      if (inputedItem) return {...inputedItem}
    }
    return item
  })
  totalSkusForExport.value = data?.value.total
  totalQuantity.value = 0
  totalPrice.value = 0
  tableLoading.value = false
}

const getInputtedSkusForUpdate = async (newModel) => {
  const stockBatchId = newModel.id
  const params = {
    type: form.type,
    stock: form.stock_id,
  }
  const { data } = await useApi(`/factory/stock-batches/${stockBatchId}/inputted-skus`, { params })
  if (isTypeImport.value) {
    inputedSkuItemsImportEdit.value = data.value?.data
  } else {
    inputedSkuItemsExportEdit.value = data.value?.data
  }
}

const getSkusForUpdate = async (newModel) => {
  const stockBatchId = newModel.id
  const params = isTypeImport.value ? filter : filterSkus
  params.type = form.type
  params.stock = form.stock_id
  tableLoading.value = true
  const { data: allSkusData } = await useApi(`/factory/stock-batches/${stockBatchId}/skus`, { params })
  if (isTypeImport.value) {
    productSkus.value = allSkusData.value?.data.map(item => {
      if (item?.id) return item
      if (inputedSkuItemsImportEdit.value.length) {
        const inputedItem = inputedSkuItemsImportEdit.value.find(inputedItem => inputedItem?.product_sku_id === item?.product_sku_id)
        if (inputedItem) {
          if (inputedItem?.id && !item?.id) delete inputedItem?.id
          return {...inputedItem}
        }
      }
      return item
    })
    totalProductSkus.value = allSkusData.value?.total
  } else {
    skusForExport.value = allSkusData.value?.data.map(item => {
      if (item?.id) return item
      if (inputedSkuItemsExportEdit.value.length) {
        const inputedItem = inputedSkuItemsExportEdit.value.find(inputedItem => inputedItem?.product_sku_id === item?.product_sku_id)
        if (inputedItem) {
          if (inputedItem?.id && !item?.id) delete inputedItem?.id
          return {...inputedItem}
        }
      }
      return item
    })
    totalSkusForExport.value = allSkusData.value?.total
  }
  
  totalQuantity.value = newModel?.quantity
  totalPrice.value = newModel?.price
  tableLoading.value = false
}

const openDialog = async (stockBatchItem = null) => {
  isDialogVisible.value = true
  if (stockBatchItem) {
    form.stock_id = stockBatchItem?.stock.id
    form.name = stockBatchItem?.name
    form.type = stockBatchItem?.type
    form.transfer_to_stock_id = stockBatchItem?.transfer_to_stock_id
    form.status = stockBatchItem?.status
    form.handle_date = new Date(stockBatchItem?.handle_date)
    form.note = stockBatchItem?.note
    form.photos = stockBatchItem?.photos
    form.reason = stockBatchItem?.reason
    await getInputtedSkusForUpdate(stockBatchItem)
    await getSkusForUpdate(stockBatchItem)
  } else {
    if (props.stockBatchAction.stock && props.stockBatchAction.type) {
      form.stock_id = props.stockBatchAction.stock
      form.type = props.stockBatchAction.type
      if (isTypeImport.value) {
        getAllSkus()
      } else {
        getSkusForExport(form.stock_id)
      }
    } else {
      form.type = STOCK_BATCH_TYPE.IMPORT
      getAllSkus()
    }
  }
}

const searchSkus = async () => {
  if (!filter?.product_id) {
    delete filter.att1_value
    delete filter.att2_value
  }
  if (!filterSkus?.product_id) {
    delete filterSkus.att1_value
    delete filterSkus.att2_value
  }
  if (props.modelValue?.id) {
    getSkusForUpdate(props.modelValue)
  } else {
    if (isTypeImport.value) {
      getAllSkus()
    } else if (!isTypeImport.value && form.stock_id) {
      getSkusForExport(form.stock_id)
    }
  }
}

const changeSkusDataForStock = async (newStock) => {
  if (newStock === form.transfer_to_stock_id) {
    form.transfer_to_stock_id = null
  }
  if (props.modelValue?.id) {
    if (!isTypeImport.value) {
      getSkusForUpdate(props.modelValue)
    }
  } else {
    if (form.type !== STOCK_BATCH_TYPE.IMPORT && newStock) {
      getSkusForExport(newStock)
    }
  }
}

const changeSkusDataForType = async (newType) => {
  if (props.modelValue?.id) {
    getSkusForUpdate(props.modelValue)
  } else {
    if (newType !== STOCK_BATCH_TYPE.IMPORT && form.stock_id) {
      getSkusForExport(form.stock_id)
    }
  }
}

const onSubmit = async () => {
  const { valid: isValid, errors } = await refForm.value?.validate()
  if (!isValid) {
    cardRef?.value?.$el.scrollTo({ top: 0, behavior: 'smooth' });
    return
  }
  loading.status = true
  validateErrors.value = null

  const skus = isTypeImport.value
    ? (props.modelValue?.id ? inputedSkuItemsImportEdit.value : inputedSkuItemsImport.value )
    : (props.modelValue?.id ? inputedSkuItemsExportEdit.value : inputedSkuItemsExport.value)
  
  let data = {}
  data.stock_batch = Object.assign({}, form)
  data.stock_batch.handle_date = formatDate(data.stock_batch.handle_date, 'YYYY-MM-DD')
  data.stock_batch.price = totalPrice.value
  data.stock_batch.quantity = totalQuantity.value
  
  let dataSkus = []
  for (const skuItem of skus) {
    if (skuItem?.quantity && skuItem?.unit_price) {
      let dataItemAdd = {
        product_id: skuItem?.product_id,
        product_sku_id: skuItem?.product_sku_id,
        product_sku_name: isTypeImport.value ? skuItem?.name : skuItem?.product_sku_name,
        quantity: isTypeImport.value ? skuItem?.quantity : skuItem?.export_quantity,
        stock_sku_quantity: isTypeExport.value ? skuItem?.quantity : null,
        unit_price: skuItem?.unit_price,
        reason: skuItem?.reason ?? ''
      }
      const item = Object.assign({}, props.modelValue?.id ? skuItem : dataItemAdd)
      dataSkus.push(item)
    }
  }
  data.stock_batch_skus = [...dataSkus]
  
  const { error } = props.modelValue?.id ?
    await useApi(`/factory/stock-batches/${props.modelValue.id}`, {
      body: data,
      method: 'PUT',
    }) : await useApi(`/factory/stock-batches`, {
      body: data,
      method: 'POST',
    })

  if (error?.value?.statusCode === 400) {
    validateErrors.value = error?.value?.data?.data
  }
  
  loading.message = error?.value?.data?.message ?? null
  loading.color = error?.value?.data?.message ? 'error' : 'success'
  loading.status = false

  if (cardRef.value && cardRef.value.$el) {
    cardRef.value.$el.scrollTo({ top: 0, behavior: 'smooth' });
  }
  if (!loading.message) {
    emit('update')
    onReset()
  }
}

defineExpose({
  openDialog,
})
</script>
<template>
  <VDialog
    :model-value="isDialogVisible"
    @update:model-value="onReset"
  >
    <!-- 👉 Dialog close btn -->
    <DialogCloseBtn @click="onReset" />

    <VCard ref="cardRef" class="pa-sm-8 pa-5">
      <template v-slot:loader="{ isActive }">
        <VProgressLinear
          absolute
          :active="isActive"
          color="purple"
          height="4"
          indeterminate
        ></VProgressLinear>
      </template>
      <!-- 👉 Title -->
      <VCardItem class="text-center pt-0">
        <VCardTitle class="text-h3">
          {{ compType }} Batch to Stock
        </VCardTitle>
      </VCardItem>

      <VCardText class="px-0">
        <AppValidateErrors v-if="validateErrors" :error-messages="validateErrors" />
        <!-- 👉 Form -->
        <VForm ref="refForm" @submit.prevent="onSubmit">
          <VRow>
            <VCol cols="12" sm="3" :class="$vuetify.display.smAndUp ? 'pb-0' : ''">
              <FactoryStockSelectInput
                v-model="form.stock_id"
                :disabled="tableLoading"
                :rules="[requiredValidator]"
                label="Stock (*)"
                placeholder="Select Stock"
                @update:modelValue="changeSkusDataForStock"
              />
            </VCol>
            <VCol cols="12" sm="3" :class="$vuetify.display.smAndUp ? 'pb-0' : ''">
              <AppSelect
                v-model="form.type"
                :items="STOCK_BATCH_TYPE_OPTIONS"
                :disabled="tableLoading"
                :rules="[requiredValidator]"
                label="Type (*)"
                placeholder="Select Type"
                clearable
                clear-icon="tabler-x"
                @update:model-value="changeSkusDataForType"
              />
            </VCol>
            <VCol v-if="isDisplayTransferStockSelect" cols="12" sm="3" :class="$vuetify.display.smAndUp ? 'pb-0' : ''">
              <StockSelectInput
                v-model="form.transfer_to_stock_id"
                density="compact"
                label="Transfer Stock (*)"
                placeholder="Select Transfer Stock"
                :disabled-item="form?.stock_id"
                :rules="[requiredValidator]"
              />
            </VCol>
            <VCol cols="12" sm="3" :class="$vuetify.display.smAndUp ? 'pb-0' : ''">
              <AppTextField
                v-model="form.name"
                label="Name (*)"
                placeholder="Type name"
                :rules="[requiredValidator]"
              />
            </VCol>
            <VCol cols="12" sm="3" :class="$vuetify.display.smAndUp ? 'pb-0' : ''">
              <VLabel class="mb-1 text-body-2 text-high-emphasis">
                Handle date (*)
              </VLabel>
              <VDateInput
                v-model="form.handle_date"
                clearable
                density="compact"
                placeholder="Select Date"
                :rules="[requiredValidator]"
              ></VDateInput>
            </VCol>
            <VCol cols="12" sm="3" :class="$vuetify.display.smAndUp ? 'pb-0' : ''">
              <DFileInput
                v-model="form.photos"
                accept="image/*"
                label="Photos"
                placeholder="Enter photos"
                :selected="form.primary"
                @update:select-value="form.primary = $event"
              />
            </VCol>
            <VCol cols="12" sm="4" :class="$vuetify.display.smAndUp ? 'pb-0' : ''">
              <AppTextarea
                v-model="form.note"
                label="Note"
                auto-grow
                placeholder="Type note"
              />
            </VCol>
            <VCol cols="12">
              <VLabel class="mb-1 text-body-2 text-high-emphasis">
                Select Skus
              </VLabel>
              <VRow class="py-4 pt-0">
                <VCol cols="12" sm="3" class="d-flex d-fa-c">
                  <div v-if="!modelValue" class="d-flex gap-3 d-fa-c">
                    <AppItemPerPage
                      v-if="!form.type || isTypeImport"
                      v-model="filter.limit"
                      :is-reset-form="isResetForm"
                    />
                    <AppItemPerPage v-else v-model="filterSkus.limit" :is-reset-form="isResetForm" />
                    <span>
                      {{ !form.type || isTypeImport ? totalProductSkus : totalSkusForExport }} Skus
                    </span>
                  </div>
                  <div v-else class="d-flex gap-3 d-fa-c">
                    <AppItemPerPage v-if="isTypeImport" v-model="filter.limit" :is-reset-form="isResetForm" />
                    <AppItemPerPage v-else v-model="filterSkus.limit" :is-reset-form="isResetForm" />
                    <span>
                      {{ isTypeImport ? totalProductSkus : totalSkusForExport }} Skus
                    </span>
                  </div>
                </VCol>
                <VCol cols="12" sm="9">
                  <VRow dense justify="end">
                    <template v-if="isTypeImport">
                      <VCol cols="12" sm="4">
                        <ProductSelectInput
                          v-model="filter.product_id"
                          clearable
                          density="compact"
                          label="Filter Product"
                          placeholder="Select Product"
                          :service="PRODUCT_SERVICE.PRODUCTION"
                          @update:modelValue="searchSkus"
                        />
                      </VCol>
                      <VCol cols="12" sm="8">
                        <ProductSkuSelectInput
                          v-model:filter="filter"
                          :att1-attrs="{
                            clearable: true,
                          }"
                          :att2-attrs="{
                            clearable: true,
                          }"
                          :productId="filter.product_id"
                          @change="searchSkus"
                        />
                      </VCol>
                    </template>
                    <template v-else>
                      <VCol cols="12" sm="4">
                        <ProductSelectInput
                          v-model="filterSkus.product_id"
                          density="compact"
                          label="Filter Product"
                          placeholder="Select Product"
                          :service="PRODUCT_SERVICE.PRODUCTION"
                          @update:modelValue="searchSkus"
                        />
                      </VCol>
                      <VCol cols="12" sm="8">
                        <ProductSkuSelectInput
                          v-model:filter="filterSkus"
                          :att1-attrs="{
                            clearable: true,
                          }"
                          :att2-attrs="{
                            clearable: true,
                          }"
                          :productId="filterSkus.product_id"
                          @change="searchSkus"
                        />
                      </VCol>
                    </template>
                  </VRow>
                </VCol>
              </VRow>

              <VDivider />

              <!-- Table data for adding -->
              <div v-if="!modelValue">
                <!-- Table data for type import -->
                <template v-if="isTypeImport">
                  <AddSKUsStockBatchTable
                    v-model:items="productSkus"
                    v-model:inputedItems="inputedSkuItemsImport"
                    v-model:filter="filter"
                    v-model:totalQuantity="totalQuantity"
                    v-model:totalPrice="totalPrice"
                    :headers="headers"
                    :items-length="totalProductSkus"
                    :loading="tableLoading"
                    @updateOptions="async ($e) => {await updateOptions($e), getAllSkus()}"
                  />
                </template>
                <!-- Table data for type export and transfer -->
                <template v-else>
                  <AddSKUsStockBatchTable
                    v-if="form.stock_id"
                    v-model:items="skusForExport"
                    v-model:inputedItems="inputedSkuItemsExport"
                    v-model:filter="filterSkus"
                    v-model:totalQuantity="totalQuantity"
                    v-model:totalPrice="totalPrice"
                    :items-length="totalSkusForExport"
                    :loading="tableLoading"
                    v-model:stock-batch-type="form.type"
                    @updateOptions="async ($e) => {
                      await updateOptionSkus($e); getSkusForExport(form.stock_id)
                    }"
                  />
                </template>
              </div>
              
              <!-- Table data for edit -->
              <div v-else>
                <!-- Table data for type import -->
                <template v-if="isTypeImport">
                  <EditSKUsStockBatchTable
                    v-model:items="productSkus"
                    v-model:inputedItems="inputedSkuItemsImportEdit"
                    v-model:filter="filter"
                    v-model:totalQuantity="totalQuantity"
                    v-model:totalPrice="totalPrice"
                    :headers="headers"
                    :items-length="totalProductSkus"
                    :loading="tableLoading"
                    @updateOptions="async ($e) => { await updateOptions($e); getSkusForUpdate(modelValue) }"
                  />
                </template>
                <!-- Table data for type export and transfer -->
                <template v-else>
                  <EditSKUsStockBatchTable
                    v-model:items="skusForExport"
                    v-model:inputedItems="inputedSkuItemsExportEdit"
                    v-model:filter="filterSkus"
                    v-model:totalQuantity="totalQuantity"
                    v-model:totalPrice="totalPrice"
                    :items-length="totalSkusForExport"
                    :loading="tableLoading"
                    v-model:stock-batch-type="form.type"
                    @updateOptions="async ($e) => { await updateOptionSkus($e); getSkusForUpdate(modelValue) }"
                  />
                </template>
              </div>
            </VCol>
            <VCol
              cols="12"
              class="text-center"
            >
              <VBtn
                :loading="loading.status"
                type="submit"
              >
                {{ compType }} Batch
              </VBtn>
            </VCol>
          </VRow>
        </VForm>
      </VCardText>
    </VCard>
  </VDialog>
  <VSnackbar
    v-model="loading.message"
    vertical
    :color="loading.color"
    :timeout="2000"
  >
    {{ loading.message }}
  </VSnackbar>
</template>

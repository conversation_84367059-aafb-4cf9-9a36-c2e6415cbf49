<?php

namespace App\Services\SellerTopupTransactions;

use App\Exceptions\InputException;
use App\Models\Bank;
use App\Models\PromotionTopup;
use App\Models\SellerTopupTransaction;
use App\Repositories\BankRepository;
use App\Repositories\PromotionTopupRepository;
use App\Repositories\SellerTopupTransactionRepository;
use App\Services\BaseAPIService;
use App\Services\Files\FileService;
use App\Traits\FilterSellerId;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class SellerTopupTransactionService extends BaseAPIService
{
    use FilterSellerId;
    protected $hasCreator = false;
    private FileService $fileService;
    protected $storeFields = [
        'seller_id',
        'amount',
        'promotion_topup_id',
        'amount_after_promotion',
        'bank_id',
        'topup_at',
        'photo',
        'status',
    ];

    protected PromotionTopupRepository $promotionTopupRepo;
    private BankRepository $bankRepo;

    public function __construct()
    {
        parent::__construct();
        $this->repo = app(SellerTopupTransactionRepository::class);
        $this->fileService = app(FileService::class);
        $this->promotionTopupRepo = app(PromotionTopupRepository::class);
        $this->bankRepo = app(BankRepository::class);
    }

    /**
     * @throws InputException
     */
    public function store($input, $user)
    {
        try {
            if ($user->parent_id) {
                throw new \Exception('Member are not allowed to top up money.');
            }
            $photo = $input['photo'];
            $input['seller_id'] = $user->id;
            $input['status'] = SellerTopupTransaction::STATUS_PENDING;
            $input['amount'] = (int)$input['amount'];
            $file = $this->fileService->save($photo, ['simple' => true]);
            $input['photo'] = $file;
            if ($input['promotion_topup_id']) {
                $input['amount_after_promotion'] = $input['amount'] + $this->calculateTopupAmount($input['promotion_topup_id'], $input['amount'], $user->id);
            } else {
                $input['amount_after_promotion'] = $input['amount'];
            }
            DB::beginTransaction();
            $topupRecord = parent::store($input, $user);
            if ($topupRecord) {
                $this->sendTelegramNotification($topupRecord);
            }
            DB::commit();
            return $topupRecord;
        } catch (\Exception $e) {
            Log::info(__CLASS__ . '@' . __FUNCTION__, $input);
            DB::rollBack();
            throw new InputException($e->getMessage());
        }
    }

    public function calculateTopupAmount($promotionTopupId, $amount, $sellerId)
    {
        $promotion = $this->promotionTopupRepo->find($promotionTopupId);
        $type = $promotion?->type;
        $promotionAmount = $promotion?->amount;
        $promotionMinimumAmount = $promotion?->minimum_amount;
        $promotionTimes = $promotion?->times;

        // check minimum amount
        if (!is_null($promotionMinimumAmount) && $amount < $promotionMinimumAmount) {
            return 0;
        }

        // check times
        if (!is_null($promotionTimes)) {
            $count = $this->repo
                ->newQuery()
                ->where('seller_id', $sellerId)
                ->where('promotion_topup_id', $promotionTopupId)
                ->whereIn('status', [
                    SellerTopupTransaction::STATUS_PENDING,
                    SellerTopupTransaction::STATUS_APPROVED,
                ])
                ->count();

            if ($count >= $promotionTimes) {
                return 0;
            }
        }

        if ($type === PromotionTopup::TYPE_ABSOLUTE_VALUE) {
            return $promotionAmount;
        }

        if ($type === PromotionTopup::TYPE_PERCENT) {
            $maximum = $promotion->maximum_amount ?? 0;
            $addAmount = ($amount * $promotionAmount) / 100;
            return ($maximum > 0 && $addAmount <= $maximum) ? $addAmount : $maximum;
        }
        return 0;
    }

    private function findBankTypeByQuery($search)
    {
        $bankTypes = ['ping' => Bank::TYPE_PINGPONG, 'pong' => Bank::TYPE_PINGPONG, 'lian' => Bank::TYPE_LIANLIAN, 'payone' => Bank::TYPE_PAYONEER, 'bank' => Bank::TYPE_BANK_VN, 'vn' => Bank::TYPE_BANK_VN];
        foreach ($bankTypes as $name => $type) {
            if (str_contains($search, $name)) {
                return $type;
            }
        }
        return null;
    }

    public function paginate($search, $page, $perPage, $columns = ['*'], $sortBy = 'id', $orderBy = 'desc'): array
    {
        $query = $this->repo->allQuery([
            'status' => $search['status'] ?? null,
            'topup_at' => $search['topup_at'] ?? null,
        ]);
        $this->filterSellerId($query);
        if (!empty($sortBy) && !empty($orderBy)) {
            $query->orderBy($sortBy, $orderBy);
        }
        if (!empty($search['query'])) {
            $promotions = $this->promotionTopupRepo->newQuery()->where(function ($query) use ($search) {
                return $query->where('name', 'like', '%' . $search['query'] . '%');
            })->select(['id'])->get()->pluck('id')->toArray();
            $bankType = $this->findBankTypeByQuery($search['query']);
            $bankIds = $this->bankRepo->newQuery()->where(function ($query) use ($search, $bankType) {
                return $query->where('name', 'like', '%' . $search['query'] . '%')->orWhere('type', $bankType);
            })->pluck('id')->toArray();
            $query->where(function ($query) use ($search, $promotions, $bankIds) {
                $query = $query->where('amount', 'like', '%' . $search['query'] . '%')
                    ->orWhere('note', 'like', '%' . $search['query'] . '%');
                if (!empty($promotions)) {
                    $query = $query->orWhereIn('promotion_topup_id', $promotions);
                }
                if (!empty($bankIds)) {
                    $query = $query->orWhereIn('bank_id', $bankIds);
                }
                return $query;
            });
        }
        $data = $query->paginate($perPage, $columns, 'page', $page);
        return [
            'total' => $data->total(),
            'data' => $data->items()
        ];
    }

    private function sendTelegramNotification($topupRecord)
    {
        $topupRecord->load(['seller', 'bank']);
        $sellerName = $topupRecord->seller->name;
        $sellerEmail = $topupRecord->seller->email;
        $amount = number_format($topupRecord->amount / 100, 2, '.', ',');
        $bankType = Bank::getBankTypeText($topupRecord->bank->type);
        $currency = Bank::getCurrencyText($topupRecord->bank->currency);
        $message = "New topup request from [{$sellerName} ({$sellerEmail})] with Total: {$amount} $currency through $bankType";
        telegram_notify('topup', $message);
    }
}

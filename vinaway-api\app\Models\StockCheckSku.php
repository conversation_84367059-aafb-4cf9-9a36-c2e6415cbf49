<?php

namespace App\Models;

use App\Traits\CreatorRelationship;
use App\Traits\Filterable;
use App\Traits\SavingCreatorUpdaterDeleterId;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class StockCheckSku extends Model
{
    use HasFactory, SoftDeletes, Filterable, CreatorRelationship, SavingCreatorUpdaterDeleterId;

    protected $table = "stock_check_skus";

    protected $fillable = [
        'stock_check_id',
        'stock_sku_id',
        'product_sku_id',
        'quantity_expect',
        'quantity_real',
        'quantity_diff',
        'note',
        'creator_id',
        'updater_id',
        'deleter_id',
    ];

    /**
     * Get the stockCheck that owns the StockCheckSku
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function stockCheck()
    {
        return $this->belongsTo(StockCheck::class, 'stock_check_id');
    }

    /**
     * Get the productSku that owns the StockCheckSku
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function productSku()
    {
        return $this->belongsTo(ProductSku::class, 'product_sku_id');
    }
}
